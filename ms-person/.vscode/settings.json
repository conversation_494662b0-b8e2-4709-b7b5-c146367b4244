{"editor.formatOnSave": true, "editor.tabSize": 2, "editor.detectIndentation": true, "editor.autoIndent": "full", "editor.indentSize": "tabSize", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "prettier.trailingComma": "none", "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "typescript.tsdk": "node_modules/typescript/lib"}