model Person {
  id            String         @id @default(uuid())
  name          String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  deleted       Boolean        @default(false)
  PersonNatural PersonNatural?
  PersonLegal   PersonLegal?
  Document      Document?
  Address       Address?
  Contact       Contact?
  StoreCredits  StoreCredit[]

  @@map("persons")
}
