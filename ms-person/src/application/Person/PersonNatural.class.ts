import { Language } from '@app/application/Language'
import {
  Person,
  type PersonNaturalCreateProps,
  type PersonNaturalResponse,
  type PersonNaturalUpdateProps,
} from '@app/application/Person'

import { PersonNatural as PersonNaturalDatabase } from '@app/domain/database/PersonNatural'

export class PersonNatural {
  public async create({
    birthDate,
    gender,
    name,
  }: PersonNaturalCreateProps): Promise<PersonNaturalResponse> {
    const model = new PersonNaturalDatabase()
    const personApp = new Person()

    const person = await personApp.create({ name })

    const personNatural = await model.create({
      birthDate,
      gender,
      name,
      personId: person.id,
    })

    return personNatural
  }

  public async findAll(): Promise<PersonNaturalResponse[]> {
    const model = new PersonNaturalDatabase()

    const personNatural = await model.findAll()

    return personNatural as unknown as PersonNaturalResponse[]
  }

  public async findById(findId: string): Promise<PersonNaturalResponse> {
    const model = new PersonNaturalDatabase()

    const { id, personId, birthDate, gender, name, createdAt, updatedAt } =
      await model.findById(findId)

    return {
      id,
      personId,
      birthDate,
      gender,
      name,
      createdAt,
      updatedAt,
    } as unknown as PersonNaturalResponse
  }

  public async findByPersonId(
    findPersonId: string,
  ): Promise<Omit<PersonNaturalResponse, 'deleted'>> {
    const model = new PersonNaturalDatabase()

    const { id, personId, birthDate, gender, name, createdAt, updatedAt } =
      await model.findByPersonId(findPersonId)

    return {
      id,
      personId,
      birthDate,
      gender,
      name,
      createdAt,
      updatedAt,
    } as unknown as PersonNaturalResponse
  }

  public async update({
    id,
    birthDate,
    gender,
    name,
    personId,
  }: PersonNaturalUpdateProps): Promise<PersonNaturalResponse> {
    const model = new PersonNaturalDatabase()
    const personApp = new Person()

    await personApp.update({
      id: personId,
      name: name,
    })

    const personNatural = await model.update({
      id,
      birthDate,
      gender,
      name,
      personId,
    })

    return personNatural
  }

  public async delete(id: string) {
    const model = new PersonNaturalDatabase()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(Language.translate('person:notFound', { id }))
    }

    const personApp = new Person()

    await personApp.delete(existingItem.personId)

    return await model.delete(id)
  }
}
