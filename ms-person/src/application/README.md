# [Application](../application)

> _**TL;DR**_ This layer is middleware between _[domain](../domain)_ package, and _[resources](../resources)_ package.

[Application Business Rules](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html), this encapsulates and implements all of the use cases of the system. These use cases orchestrate the flow of data to and from the entities

## Configuration

- _[eslint](../../.eslint.config/app/resources)_
- _[jest](../../.jest/jest.config.json)_
