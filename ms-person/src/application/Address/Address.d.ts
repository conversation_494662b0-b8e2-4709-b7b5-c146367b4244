export type AddressModel = {
  id: string
  personId: string
  line1: string
  number: string
  line2: string
  city: string
  region: string
  country: string
  postalCode: string
  createdAt: Date
  updatedAt: Date
}

export type AddressResponse = AddressModel

export type AddressCreateProps = Omit<
  AddressModel,
  'id' | 'createdAt' | 'updatedAt' | 'deleted'
>

export type AddressUpdateProps = Omit<
  AddressModel,
  'createdAt' | 'updatedAt' | 'deleted'
>
