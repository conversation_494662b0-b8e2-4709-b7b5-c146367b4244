import type { UseStoreCreditDto } from '@app/application/StoreCredit'
import { UseStoreCreditSchema } from '@app/application/StoreCredit/StoreCredit.schema'

import { StoreCredit } from '@app/domain/database/StoreCredit'

export class UseStoreCredit {
  public async use(input: unknown) {
    const dto: UseStoreCreditDto = UseStoreCreditSchema.parse(input)

    const model = new StoreCredit()

    const updatedCredit = await model.useCredit(dto)

    return {
      ...updatedCredit,
      balance: Number(updatedCredit.balance),
    }
  }
}
