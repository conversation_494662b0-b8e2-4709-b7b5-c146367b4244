import { Decimal } from '@prisma/client/runtime/library'
import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination/PaginationSchema.schema'

export const StoreCreditBaseSchema = z.object({
  personId: z.string().uuid(),
  balance: z
    .number()
    .nonnegative()
    .transform((val) => new Decimal(val.toFixed(2))),
  currency: z.string().default('BRL'),
  issuingStoreLegalPersonId: z.string().uuid(),
  originatingProposalId: z.string().uuid(),
  notes: z.string().optional(),
})

export const CreateStoreCreditSchema = StoreCreditBaseSchema

export const UpdateStoreCreditSchema = StoreCreditBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  personId: true,
  balance: true,
  currency: true,
  issuingStoreLegalPersonId: true,
  originatingProposalId: true,
  notes: true,
})

export const UseStoreCreditSchema = z.object({
  id: z.string().uuid(),
  amountToUse: z
    .number()
    .positive()
    .transform((val) => new Decimal(val.toFixed(2))),
  storeLegalPersonId: z.string().uuid(),
})

export const FilterStoreCreditSchema = createFilterSchema({
  personId: z.string().uuid().optional(),
  issuingStoreLegalPersonId: z.string().uuid().optional(),
  currency: z.string().optional(),
})
