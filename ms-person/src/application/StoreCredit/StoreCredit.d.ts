import type { z } from 'zod'

import type {
  CreateStoreCreditSchema,
  FilterStoreCreditSchema,
  UpdateStoreCreditSchema,
  UseStoreCreditSchema,
} from '@app/application/StoreCredit/StoreCredit.schema'

export type CreateStoreCreditDto = z.infer<typeof CreateStoreCreditSchema>
export type UpdateStoreCreditDto = z.infer<typeof UpdateStoreCreditSchema>
export type FilterStoreCreditDto = z.infer<typeof FilterStoreCreditSchema>
export type UseStoreCreditDto = z.infer<typeof UseStoreCreditSchema>

export type FetchStoreCreditsProps = FilterStoreCreditDto

export type StoreCreditData = {
  id: string
  personId: string
  balance: number
  currency: string
  issuingStoreLegalPersonId: string
  originatingProposalId: string
  notes?: string | null
  createdAt: Date
  updatedAt: Date
}
