import type { CreateStoreCreditDto } from '@app/application/StoreCredit'
import { CreateStoreCreditSchema } from '@app/application/StoreCredit/StoreCredit.schema'

import { StoreCredit } from '@app/domain/database/StoreCredit'

export class AddStoreCredit {
  public async add(input: unknown) {
    const dto: CreateStoreCreditDto = CreateStoreCreditSchema.parse(input)

    const model = new StoreCredit()

    return model.create(dto)
  }
}
