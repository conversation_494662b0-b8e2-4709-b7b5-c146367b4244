import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  type IdentifierDto,
  PersonNatural as PersonNaturalApplication,
  type PersonNaturalResponse,
  type PersonRequest,
} from '@app/application/Person'

import type { PersonNaturalPostBody } from '@app/resources/Person/Person'

export class Person {
  @Post('/person/natural')
  public async create({ request, response }: ResourceMethodProps) {
    try {
      const personNatural = request.body<PersonNaturalPostBody>()

      response.send<PersonNaturalResponse>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_201_0201}`,
        ),
        data: await new PersonNaturalApplication().create(personNatural),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id: 'body' }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person/natural')
  public async findAll({ response }: ResourceMethodProps) {
    try {
      response.send<PersonNaturalResponse[]>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonNaturalApplication().findAll(),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFoundAll'),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person/natural/:id')
  public async findById({ request, response }: ResourceMethodProps) {
    const { id } = request.params<IdentifierDto>()

    try {
      response.send<PersonNaturalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonNaturalApplication().findById(id),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', {
            id,
          }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person/natural/personId/:personId')
  public async findByPersonId({ request, response }: ResourceMethodProps) {
    try {
      response.send<PersonNaturalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonNaturalApplication().findByPersonId(
          request.params<PersonRequest>().personId,
        ),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', {
            id: request.params<PersonRequest>().personId,
          }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/person/natural/:personId')
  public async update({ request, response }: ResourceMethodProps) {
    try {
      response.send<PersonNaturalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonNaturalApplication().update({
          ...request.body(),
          id: request.params<PersonRequest>().personId,
        }),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id: 'body' }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/person/natural/:id')
  public async delete({ request, response }: ResourceMethodProps) {
    try {
      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonNaturalApplication().delete(
          request.params<IdentifierDto>().id,
        ),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id: 'body' }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
