import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Get, Post } from '@thrift/common/engines/Server'

import {
  AddStoreCredit,
  GetStoreCreditBalance,
  UseStoreCredit,
} from '@app/application/StoreCredit'
import { Language } from '@app/application/Language'

import type {
  CreateStoreCreditDto,
  PersonIdentifierDto,
  StoreCreditFilterDto,
  UseStoreCreditDto,
} from '@app/resources/StoreCredit/StoreCredit'

export class StoreCreditResource {
  @Get('/persons/:personId/store-credits')
  public async getPersonStoreCredits({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { personId } = request.params<PersonIdentifierDto>()
      const { issuingStoreLegalPersonId, currency } =
        request.query<StoreCreditFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const filter = {
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        personId,
        issuingStoreLegalPersonId,
        currency,
      }

      ResponseHelper.sendSuccessResponse(
        response,
        await new GetStoreCreditBalance().find(filter),
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'store credit',
        Language.translate,
      )
    }
  }

  @Get('/persons/:personId/store-credits/balance')
  public async getPersonStoreCreditBalance({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { personId } = request.params<PersonIdentifierDto>()
      const { issuingStoreLegalPersonId } = request.query<StoreCreditFilterDto>()

      ResponseHelper.sendSuccessResponse(
        response,
        await new GetStoreCreditBalance().getBalance(personId, {
          issuingStoreLegalPersonId,
        }),
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'store credit',
        Language.translate,
      )
    }
  }

  @Post('/persons/:personId/store-credits')
  public async postPersonStoreCredit({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { personId } = request.params<PersonIdentifierDto>()
      const createStoreCreditDto = request.body<CreateStoreCreditDto>()

      const data = await new AddStoreCredit().add({
        ...createStoreCreditDto,
        personId,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        {
          ...data,
          balance: Number(data.balance),
        },
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'store credit' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'store credit',
        Language.translate,
      )
    }
  }

  @Post('/persons/:personId/store-credits/use')
  public async postUseStoreCredit({ request, response }: ResourceMethodProps) {
    try {
      const { personId } = request.params<PersonIdentifierDto>()
      const useStoreCreditDto = request.body<UseStoreCreditDto>()

      // For this endpoint, we need to find the appropriate store credit
      // This is a simplified implementation - in practice, you might want to
      // specify which credit to use or have more complex logic
      const balanceService = new GetStoreCreditBalance()
      const credits = await balanceService.getBalance(personId, {
        issuingStoreLegalPersonId: useStoreCreditDto.storeLegalPersonId,
      })

      if (credits.storeCredits.length === 0) {
        throw new ReferenceError('No store credits found for this store')
      }

      // Use the first available credit (you might want more sophisticated logic)
      const creditToUse = credits.storeCredits[0]

      const data = await new UseStoreCredit().use({
        id: creditToUse.id,
        amountToUse: useStoreCreditDto.amountToUse,
        storeLegalPersonId: useStoreCreditDto.storeLegalPersonId,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'store credit',
        Language.translate,
      )
    }
  }
}
