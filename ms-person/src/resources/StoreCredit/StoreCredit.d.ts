export type PersonIdentifierDto = {
  personId: string
}

export type StoreCreditIdentifierDto = {
  id: string
}

export type StoreCreditFilterDto = {
  issuingStoreLegalPersonId?: string
  currency?: string
}

export type CreateStoreCreditDto = {
  balance: number
  currency?: string
  issuingStoreLegalPersonId: string
  originatingProposalId: string
  notes?: string
}

export type UseStoreCreditDto = {
  amountToUse: number
  storeLegalPersonId: string
}

export type StoreCreditResponseDto = {
  id: string
  personId: string
  balance: number
  currency: string
  issuingStoreLegalPersonId: string
  originatingProposalId: string
  notes?: string | null
  createdAt: Date
  updatedAt: Date
}
