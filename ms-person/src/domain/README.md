# [Domain](../domain)

> _**TL;DR**_ This layer is responsable to abstract all external dependencies.

[Enterprise Business Rules](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html), can be an object with methods, or it can be a set of data structures and functions. It doesn’t matter so long as the entities could be used by many different applications in the enterprise.

## Configuration

- _[eslint](../../.eslint.config/app/domain)_
