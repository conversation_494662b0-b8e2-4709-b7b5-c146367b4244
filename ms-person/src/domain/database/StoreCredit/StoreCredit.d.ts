import type { Decimal } from '@prisma/client/runtime/library'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type StoreCreditModel = {
  id: string
  personId: string
  balance: Decimal
  currency: string
  issuingStoreLegalPersonId: string
  originatingProposalId: string
  notes?: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateStoreCreditDto = {
  personId: string
  balance: Decimal
  currency?: string
  issuingStoreLegalPersonId: string
  originatingProposalId: string
  notes?: string
}

export type UpdateStoreCreditDto = Partial<
  Omit<CreateStoreCreditDto, 'personId' | 'originatingProposalId'>
> & {
  id: string
}

export type UseStoreCreditDto = {
  id: string
  amountToUse: Decimal
  storeLegalPersonId: string
}

export type FetchStoreCreditsProps = PaginationParams & {
  personId?: string
  issuingStoreLegalPersonId?: string
  currency?: string
}
