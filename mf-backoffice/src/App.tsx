import { Navigate, Route, Routes } from "react-router-dom";
import Dashboard from "@app/pages/Dashboard/Dashboard.pages";
import Settings from "@app/pages/Settings/Settings.pages";
import Profile from "@app/pages/Profile/Profile.pages";
import LoggedLayout from "@app/LoggedLayout";

const App = () => {
  return (
    <Routes>
      <Route element={<LoggedLayout />}>
        <Route path="/" element={<Navigate to="/dashboard" />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/profile" element={<Profile />} />
      </Route>
    </Routes>
  );
};

export default App;
