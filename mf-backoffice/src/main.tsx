import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import '@app/domain/locales/i18n/i18next'
import { BrowserRouter } from 'react-router-dom'
import '@app/styles/globals.css'

import { ToastProvider } from '@thrift/design-system/packages/molecules/Toast'
import App from '@app/App'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient()

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </ToastProvider>
    </QueryClientProvider>
  </StrictMode>,
)
