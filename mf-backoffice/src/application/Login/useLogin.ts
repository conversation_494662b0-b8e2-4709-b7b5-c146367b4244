import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useLoginSchema,
  LoginSchemaType,
} from "@app/application/Login/Login.schemas";
import { useLoginMutation } from "@app/domain/ProductCategory";
import { useToast } from "@thrift/design-system/packages/molecules/Toast";
import { useLanguage } from "@app/application/Language";

export const useLogin = () => {
  const { showToast } = useToast();
  const { translate } = useLanguage();
  const LoginSchema = useLoginSchema();
  const {
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  });

  const { postLoginAsync, loading } = useLoginMutation();

  const onSubmit = async (data: LoginSchemaType) => {
    try {
      await postLoginAsync(data);
    } catch (error) {
      showToast({
        title: translate("login:loginFailedTitle"),
        message: translate("login:loginFailedMessage"),
        type: "error",
      });
    }
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    errors,
    loading,
    setValue,
    getValues,
  };
};
