import api from "@app/domain/services/api";
import {
  StockLocationParams,
  StockLocationListParams,
} from "@app/domain/StockLocation/StockLocation";

export const getStockLocation = async (params: StockLocationListParams) => {
  const response = await api.get(
    `/stock/stock-locations?page=${params.page}&perPage=${params.perPage}`
  );

  return response?.data;
};

export const getStockLocationById = async (id: string) => {
  const response = await api.get(`/stock/stock-locations/${id}`);

  return response?.data;
};

export const postStockLocation = async (
  params: Omit<StockLocationParams, "id">
) => {
  const response = await api.post(`/stock/stock-locations`, params);

  return response?.data;
};

export const putStockLocation = async (params: StockLocationParams) => {
  const response = await api.put(`/stock/stock-locations/${params.id}`, {
    name: params.name,
    address: params.address,
    type: params.type,
  });

  return response?.data;
};

export const deleteStockLocation = async (id: string) => {
  const response = await api.delete(`/stock/stock-locations/${id}`);

  return response?.data;
};
