import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  TenantContactListParams,
  TenantContact,
} from '@app/domain/wallet/TenantContact/TenantContact'
import {
  postTenantContact,
  putTenantContact,
  getTenantContact,
  getTenantContactById,
  deleteTenantContact,
} from '@app/domain/wallet/TenantContact/TenantContact.api'

export const useGetTenantContactQuery = (params: TenantContactListParams) => {
  const getTenantContactFn = useQuery({
    queryKey: ['getTenantContact', params],
    queryFn: () => getTenantContact(params),
    enabled: params != null,
  })

  return getTenantContactFn
}

export const useGetTenantContactByIdQuery = (id: string) => {
  const getTenantContactByIdFn = useQuery({
    queryKey: ['getTenantContactById', id],
    queryFn: () => getTenantContactById(id),
    enabled: id != null,
  })

  return getTenantContactByIdFn
}

export const usePostTenantContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantContact) => postTenantContact(params),
  })

  const postTenantContactAsync = useCallback(
    async (props: TenantContact) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    postTenantContactAsync,
    loading: isPending,
  }
}

export const usePutTenantContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantContact) => putTenantContact(params),
  })

  const putTenantContactAsync = useCallback(
    async (props: TenantContact) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    putTenantContactAsync,
    loading: isPending,
  }
}

export const useDeleteTenantContactMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTenantContact(id),
  })

  const deleteTenantContactAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteTenantContactAsync,
    loading: isPending,
  }
}
