import api from '@app/domain/services/api'
import {
  PaymentMethod,
  PaymentMethodListParams,
} from '@app/domain/wallet/PaymentMethod/PaymentMethod'

export const getPaymentMethod = async (params: PaymentMethodListParams) => {
  const response = await api.get(
    `/wallet/payment-methods?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data
}

export const getPaymentMethodById = async (id: string) => {
  const response = await api.get(`/wallet/payment-methods/${id}`)

  return response?.data
}

export const postPaymentMethod = async (params: Omit<PaymentMethod, 'id'>) => {
  const response = await api.post(`/wallet/payment-methods`, params)

  return response?.data
}

export const putPaymentMethod = async (params: PaymentMethod) => {
  const { id, ...body } = params
  const response = await api.put(`/wallet/payment-methods/${id}`, body)

  return response?.data
}

export const deletePaymentMethod = async (id: string) => {
  const response = await api.delete(`/wallet/payment-methods/${id}`)

  return response?.data
}
