import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { TenantListParams, Tenant } from '@app/domain/wallet/Tenant/Tenant'
import {
  postTenant,
  putTenant,
  getTenant,
  getTenantById,
  deleteTenant,
} from '@app/domain/wallet/Tenant/Tenant.api'

export const useGetTenantQuery = (params: TenantListParams) => {
  const getTenantFn = useQuery({
    queryKey: ['getTenant', params],
    queryFn: () => getTenant(params),
    enabled: params != null,
  })

  return getTenantFn
}

export const useGetTenantByIdQuery = (id: string) => {
  const getTenantByIdFn = useQuery({
    queryKey: ['getTenantById', id],
    queryFn: () => getTenantById(id),
    enabled: id != null,
  })

  return getTenantByIdFn
}

export const usePostTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Tenant) => postTenant(params),
  })

  const postTenantAsync = useCallback(
    async (props: Tenant) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    postTenantAsync,
    loading: isPending,
  }
}

export const usePutTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Tenant) => putTenant(params),
  })

  const putTenantAsync = useCallback(
    async (props: Tenant) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    putTenantAsync,
    loading: isPending,
  }
}

export const useDeleteTenantMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTenant(id),
  })

  const deleteTenantAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteTenantAsync,
    loading: isPending,
  }
}
