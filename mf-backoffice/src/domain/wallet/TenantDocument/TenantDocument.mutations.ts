import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  TenantDocumentListParams,
  TenantDocument,
} from '@app/domain/wallet/TenantDocument/TenantDocument'
import {
  postTenantDocument,
  putTenantDocument,
  getTenantDocument,
  getTenantDocumentById,
  deleteTenantDocument,
} from '@app/domain/wallet/TenantDocument/TenantDocument.api'

export const useGetTenantDocumentQuery = (params: TenantDocumentListParams) => {
  const getTenantDocumentFn = useQuery({
    queryKey: ['getTenantDocument', params],
    queryFn: () => getTenantDocument(params),
    enabled: params != null,
  })

  return getTenantDocumentFn
}

export const useGetTenantDocumentByIdQuery = (id: string) => {
  const getTenantDocumentByIdFn = useQuery({
    queryKey: ['getTenantDocumentById', id],
    queryFn: () => getTenantDocumentById(id),
    enabled: id != null,
  })

  return getTenantDocumentByIdFn
}

export const usePostTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantDocument) => postTenantDocument(params),
  })

  const postTenantDocumentAsync = useCallback(
    async (props: TenantDocument) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    postTenantDocumentAsync,
    loading: isPending,
  }
}

export const usePutTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: TenantDocument) => putTenantDocument(params),
  })

  const putTenantDocumentAsync = useCallback(
    async (props: TenantDocument) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    putTenantDocumentAsync,
    loading: isPending,
  }
}

export const useDeleteTenantDocumentMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTenantDocument(id),
  })

  const deleteTenantDocumentAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteTenantDocumentAsync,
    loading: isPending,
  }
}
