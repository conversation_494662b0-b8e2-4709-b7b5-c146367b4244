import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  StockLocationListParams,
  StockLocation,
} from '@app/domain/stock/StockLocation/StockLocation'
import {
  postStockLocation,
  putStockLocation,
  getStockLocation,
  getStockLocationById,
  deleteStockLocation,
} from '@app/domain/stock/StockLocation/StockLocation.api'

export const useGetStockLocationQuery = (params: StockLocationListParams) => {
  const getStockLocationFn = useQuery({
    queryKey: ['getStockLocation', params],
    queryFn: () => getStockLocation(params),
    enabled: params != null,
  })

  return getStockLocationFn
}

export const useGetStockLocationByIdQuery = (id: string) => {
  const getStockLocationByIdFn = useQuery({
    queryKey: ['getStockLocationById', id],
    queryFn: () => getStockLocationById(id),
    enabled: id != null,
  })

  return getStockLocationByIdFn
}

export const usePostStockLocationMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: StockLocation) => postStockLocation(params),
  })

  const postStockLocationAsync = useCallback(
    async (props: StockLocation) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    postStockLocationAsync,
    loading: isPending,
  }
}

export const usePutStockLocationMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: StockLocation) => putStockLocation(params),
  })

  const putStockLocationAsync = useCallback(
    async (props: StockLocation) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    putStockLocationAsync,
    loading: isPending,
  }
}

export const useDeleteStockLocationMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteStockLocation(id),
  })

  const deleteStockLocationAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteStockLocationAsync,
    loading: isPending,
  }
}
