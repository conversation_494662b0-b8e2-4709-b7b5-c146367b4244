import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { ProductListParams, Product } from '@app/domain/stock/Product/Product'
import {
  postProduct,
  putProduct,
  getProduct,
  getProductById,
  deleteProduct,
} from '@app/domain/stock/Product/Product.api'

export const useGetProductQuery = (params: ProductListParams) => {
  const getProductFn = useQuery({
    queryKey: ['getProduct', params],
    queryFn: () => getProduct(params),
    enabled: params != null,
  })

  return getProductFn
}

export const useGetProductByIdQuery = (id: string) => {
  const getProductByIdFn = useQuery({
    queryKey: ['getProductById', id],
    queryFn: () => getProductById(id),
    enabled: id != null,
  })

  return getProductByIdFn
}

export const usePostProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Product) => postProduct(params),
  })

  const postProductAsync = useCallback(
    async (props: Product) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    postProductAsync,
    loading: isPending,
  }
}

export const usePutProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Product) => putProduct(params),
  })

  const putProductAsync = useCallback(
    async (props: Product) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    putProductAsync,
    loading: isPending,
  }
}

export const useDeleteProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteProduct(id),
  })

  const deleteProductAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)
      return response.data
    },
    [mutateAsync],
  )

  return {
    deleteProductAsync,
    loading: isPending,
  }
}
