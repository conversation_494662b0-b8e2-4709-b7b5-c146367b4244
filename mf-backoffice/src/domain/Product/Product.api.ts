import api from "@app/domain/services/api";
import { ProductParams, ProductListParams } from "@app/domain/Product/Product";
import { ca } from "zod/v4/locales";

export const getProduct = async (params: ProductListParams) => {
  const response = await api.get(
    `/stock/product?page=${params.page}&perPage=${params.perPage}`
  );

  return response?.data;
};

export const getProductById = async (id: string) => {
  const response = await api.get(`/stock/product/${id}`);

  return response?.data;
};

export const postProduct = async (params: Omit<ProductParams, "id">) => {
  const response = await api.post(`/stock/product`, params);

  return response?.data;
};

export const putProduct = async (params: ProductParams) => {
  const response = await api.put(`/stock/product/${params.id}`, {
    name: params.name,
    description: params.description,
    categoryId: params.categoryId,
    sku: params.sku,
  });

  return response?.data;
};

export const deleteProduct = async (id: string) => {
  const response = await api.delete(`/stock/product/${id}`);

  return response?.data;
};
