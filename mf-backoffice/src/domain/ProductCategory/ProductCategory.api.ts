import api from "@app/domain/services/api";
import {
  ProductCategoryParams,
  ProductCategoryListParams,
} from "@app/domain/ProductCategory/ProductCategory";

export const getProductCategory = async (params: ProductCategoryListParams) => {
  const response = await api.get(
    `/stock/product-categories?page=${params.page}&perPage=${params.perPage}`
  );

  return response?.data;
};

export const getProductCategoryById = async (id: string) => {
  const response = await api.get(`/stock/product-categories/${id}`);

  return response?.data;
};

export const postProductCategory = async (
  params: Omit<ProductCategoryParams, "id">
) => {
  const response = await api.post(`/stock/product-categories`, params);

  return response?.data;
};

export const putProductCategory = async (params: ProductCategoryParams) => {
  const response = await api.put(`/stock/product-categories/${params.id}`, {
    name: params.name,
    description: params.description,
  });

  return response?.data;
};

export const deleteProductCategory = async (id: string) => {
  const response = await api.delete(`/stock/product-categories/${id}`);

  return response?.data;
};
