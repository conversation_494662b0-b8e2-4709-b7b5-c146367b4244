import api from "@app/domain/services/api";
import { StockParams, StockListParams } from "@app/domain/Stock/Stock";

export const getStock = async (params: StockListParams) => {
  const response = await api.get(
    `/stock/stocks?page=${params.page}&perPage=${params.perPage}`
  );

  return response?.data;
};

export const getStockById = async (id: string) => {
  const response = await api.get(`/stock/stocks/${id}`);

  return response?.data;
};

export const postStock = async (params: Omit<StockParams, "id">) => {
  const response = await api.post(`/stock/stocks`, params);

  return response?.data;
};

export const putStock = async (params: StockParams) => {
  const response = await api.put(`/stock/stocks/${params.id}`, {
    productId: params.productId,
    locationId: params.locationId,
    quantity: params.quantity,
  });

  return response?.data;
};

export const deleteStock = async (id: string) => {
  const response = await api.delete(`/stock/stocks/${id}`);

  return response?.data;
};
