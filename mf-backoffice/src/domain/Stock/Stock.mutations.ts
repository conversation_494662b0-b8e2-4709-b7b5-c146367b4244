import { useCallback } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { StockListParams, StockParams } from "@app/domain/Stock/Stock";
import {
  postStock,
  putStock,
  getStock,
  getStockById,
  deleteStock,
} from "@app/domain/Stock/Stock.api";

export const useGetStockQuery = (params: StockListParams) => {
  const getStockFn = useQuery({
    queryKey: ["getStock", params],
    queryFn: () => getStock(params),
    enabled: params != null,
  });

  return getStockFn;
};

export const useGetStockByIdQuery = (id: string) => {
  const getStockByIdFn = useQuery({
    queryKey: ["getStockById", id],
    queryFn: () => getStockById(id),
    enabled: id != null,
  });

  return getStockByIdFn;
};

export const usePostStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: StockParams) => postStock(params),
  });

  const postStockAsync = useCallback(
    async (props: StockParams) => {
      const response = await mutateAsync(props);
      return response.data;
    },
    [mutateAsync]
  );

  return {
    postStockAsync,
    loading: isPending,
  };
};

export const usePutStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: StockParams) => putStock(params),
  });

  const putStockAsync = useCallback(
    async (props: StockParams) => {
      const response = await mutateAsync(props);
      return response.data;
    },
    [mutateAsync]
  );

  return {
    putStockAsync,
    loading: isPending,
  };
};

export const useDeleteStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteStock(id),
  });

  const deleteStockAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props);
      return response.data;
    },
    [mutateAsync]
  );

  return {
    deleteStockAsync,
    loading: isPending,
  };
};
