import { resolve } from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

const rootPath = resolve(__dirname, ".");
const sourcePath = resolve(rootPath, "./src");

// https://vite.dev/config/
export default defineConfig({
  root: rootPath,
  plugins: [react(), tailwindcss()],
  build: {
    sourcemap: true,
    emptyOutDir: true,
    outDir: resolve(rootPath, "./dist"),
  },
  resolve: {
    alias: { "@app": sourcePath },
  },
});
