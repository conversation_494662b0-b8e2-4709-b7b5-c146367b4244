model PaymentMethod {
  id        String   @id @default(uuid())
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  provider  String
  config    <PERSON>son
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  paymentHistory PaymentHistory[]

  @@map("payment_methods")
}