model PaymentHistory {
  id              String        @id @default(uuid())
  tenant          Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId        String
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId String
  amount          Decimal
  currency        String
  status          String
  externalId      String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  deleted         Boolean       @default(false)

  @@map("payment_history")
}