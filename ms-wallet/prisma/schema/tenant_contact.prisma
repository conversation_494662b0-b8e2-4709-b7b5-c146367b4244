model TenantContact {
  id        String      @id @default(uuid())
  tenant    Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  type      String
  value     String
  isPrimary Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  deleted   <PERSON>olean     @default(false)

  @@unique([tenantId, type, value])
  @@map("tenant_contacts")
}