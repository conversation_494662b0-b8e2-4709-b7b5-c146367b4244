model Domain {
  id        String     @id @default(uuid())
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  name      String
  isPrimary <PERSON><PERSON><PERSON>    @default(false)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  deleted   <PERSON><PERSON>an    @default(false)

  @@unique([tenantId, name])
  @@map("domains")
}