// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  binaryTargets   = ["native", "linux-musl-arm64-openssl-3.0.x"]
  previewFeatures = []
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}
