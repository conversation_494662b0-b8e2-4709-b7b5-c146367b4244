model Tenant {
  id          String   @id @default(uuid())
  name        String
  tenantUid   String   @unique
  balance     Decimal  @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     <PERSON><PERSON><PERSON>  @default(false)

  contacts       TenantContact[]
  documents      TenantDocument[]
  domains        Domain[]
  paymentMethods PaymentMethod[]
  paymentHistory PaymentHistory[]

  @@map("tenants")
}