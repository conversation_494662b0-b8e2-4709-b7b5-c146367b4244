model TenantDocument {
  id        String    @id @default(uuid())
  tenant    Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  type      String
  value     String
  issuedAt  DateTime?
  expiresAt DateTime?
  isPrimary Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deleted   Boolean   @default(false)

  @@unique([tenantId, type, value])
  @@map("tenant_documents")
}