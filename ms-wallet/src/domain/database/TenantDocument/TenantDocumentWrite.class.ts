import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'

import type {
  CreateTenantDocumentDto,
  TenantDocumentModel,
  UpdateTenantDocumentDto,
} from '@app/domain/database/TenantDocument'
import { TenantDocumentBase } from '@app/domain/database/TenantDocument/TenantDocumentBase.class'
import i18next from '@app/domain/locales/i18n/i18next'

export class TenantDocumentWrite extends TenantDocumentBase {
  public async create({
    tenantId,
    type,
    value,
    issuedAt,
    expiresAt,
    isPrimary = false,
  }: CreateTenantDocumentDto): Promise<TenantDocumentModel> {
    const existingDocument = await this.findByTypeAndValue(
      tenantId,
      type,
      value,
    )

    if (existingDocument) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'tenant document',
          field: 'type and value',
        }),
      )
    }

    if (isPrimary) {
      await this.prisma.tenantDocument.updateMany({
        where: { tenantId, type, isPrimary: true },
        data: { isPrimary: false },
      })
    }

    const tenantDocument = await this.prisma.tenantDocument.create({
      data: {
        tenant: {
          connect: { id: tenantId },
        },
        type,
        value,
        issuedAt,
        expiresAt,
        isPrimary,
      },
    })

    return tenantDocument
  }

  private async validateTypeAndValue(
    tenantId: string,
    id: string,
    type?: string,
    value?: string,
  ): Promise<{ type?: string; value?: string }> {
    const updateData: Record<string, unknown> = {}

    if (type !== undefined && value !== undefined) {
      const documentWithSameTypeAndValue = await this.findByTypeAndValue(
        tenantId,
        type,
        value,
      )

      if (
        documentWithSameTypeAndValue &&
        documentWithSameTypeAndValue.id !== id
      ) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'tenant document',
            field: 'type and value',
          }),
        )
      }
      updateData.type = type
      updateData.value = value
    } else if (type !== undefined) {
      updateData.type = type
    } else if (value !== undefined) {
      updateData.value = value
    }

    return updateData
  }

  private async handlePrimaryStatus(
    tenantId: string,
    id: string,
    isPrimary?: boolean,
    type?: string,
    existingType?: string,
  ): Promise<{ isPrimary?: boolean }> {
    const updateData: Record<string, unknown> = {}

    if (isPrimary !== undefined) {
      updateData.isPrimary = isPrimary

      if (isPrimary) {
        await this.prisma.tenantDocument.updateMany({
          where: {
            tenantId: tenantId,
            type: type || existingType,
            isPrimary: true,
            id: { not: id },
          },
          data: { isPrimary: false },
        })
      }
    }

    return updateData
  }

  public async update({
    id,
    type,
    value,
    issuedAt,
    expiresAt,
    isPrimary,
  }: UpdateTenantDocumentDto): Promise<TenantDocumentModel> {
    const existingDocument = await this.findById(id)

    const typeValueData = await this.validateTypeAndValue(
      existingDocument.tenantId,
      id,
      type,
      value,
    )

    const primaryStatusData = await this.handlePrimaryStatus(
      existingDocument.tenantId,
      id,
      isPrimary,
      type,
      existingDocument.type,
    )

    const updateData = {
      ...typeValueData,
      ...primaryStatusData,
      ...(issuedAt !== undefined && { issuedAt }),
      ...(expiresAt !== undefined && { expiresAt }),
    }

    const tenantDocument = await this.prisma.tenantDocument.update({
      where: { id },
      data: updateData,
    })

    return tenantDocument
  }

  public async delete(id: string): Promise<TenantDocumentModel> {
    await this.findById(id)

    const tenantDocument = await this.prisma.tenantDocument.update({
      where: { id },
      data: { deleted: true },
    })

    return tenantDocument
  }
}
