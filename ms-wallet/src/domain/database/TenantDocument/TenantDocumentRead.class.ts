import type { FetchTenantDocumentsProps } from '@app/domain/database/TenantDocument'
import { TenantDocumentBase } from '@app/domain/database/TenantDocument/TenantDocumentBase.class'

export class TenantDocumentRead extends TenantDocumentBase {
  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    id,
    tenantId,
  }: FetchTenantDocumentsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(id && { id }),
      ...(tenantId && { tenantId }),
      ...(search && {
        OR: [{ type: { contains: search } }, { value: { contains: search } }],
      }),
    }

    const [tenantDocuments, totalItems] = await this.prisma.$transaction([
      this.prisma.tenantDocument.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.tenantDocument.count({ where }),
    ])

    return {
      items: tenantDocuments,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }
}
