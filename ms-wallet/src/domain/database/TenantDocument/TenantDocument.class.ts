import type {
  CreateTenantDocumentDto,
  FetchTenantDocumentsProps,
  UpdateTenantDocumentDto,
} from '@app/domain/database/TenantDocument/TenantDocument'
import { TenantDocumentBase } from '@app/domain/database/TenantDocument/TenantDocumentBase.class'
import { TenantDocumentRead } from '@app/domain/database/TenantDocument/TenantDocumentRead.class'
import { TenantDocumentWrite } from '@app/domain/database/TenantDocument/TenantDocumentWrite.class'

export class TenantDocument extends TenantDocumentBase {
  private readonly readOperations: TenantDocumentRead
  private readonly writeOperations: TenantDocumentWrite

  constructor() {
    super()
    this.readOperations = new TenantDocumentRead()
    this.writeOperations = new TenantDocumentWrite()
  }

  public async find(props: FetchTenantDocumentsProps) {
    return this.readOperations.find(props)
  }

  public async create(dto: CreateTenantDocumentDto) {
    return this.writeOperations.create(dto)
  }

  public async update(dto: UpdateTenantDocumentDto) {
    return this.writeOperations.update(dto)
  }

  public async delete(id: string) {
    return this.writeOperations.delete(id)
  }
}
