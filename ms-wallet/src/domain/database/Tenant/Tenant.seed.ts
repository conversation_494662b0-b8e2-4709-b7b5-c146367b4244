import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  // Create a sample tenant
  await prisma.tenant.upsert({
    where: { tenantUid: 'pe-de-feijao' },
    update: {},
    create: {
      id: 'e2d4f6g8-h0j2-4k6l-8m0n-o2p4q6r8s0t2',
      name: '<PERSON><PERSON>',
      tenantUid: 'pe-de-feijao',
      balance: 1000.0,
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
