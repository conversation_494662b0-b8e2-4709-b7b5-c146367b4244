import { PrismaClient } from '@prisma/client'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateTenantDto,
  FetchTenantsProps,
  TenantModel,
  UpdateTenantDto,
} from '@app/domain/database/Tenant'
import i18next from '@app/domain/locales/i18n/i18next'

export class Tenant {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<TenantModel> {
    const tenant = await this.prisma.tenant.findFirst({
      where: { id, deleted: false },
    })

    if (!tenant) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'tenant' }),
      )
    }

    return tenant
  }

  public async findByTenantUid(tenantUid: string): Promise<TenantModel | null> {
    return this.prisma.tenant.findFirst({
      where: { tenantUid, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    id,
  }: FetchTenantsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(id && { id }),
      ...(search && {
        OR: [
          { name: { contains: search } },
          { tenantUid: { contains: search } },
        ],
      }),
    }

    const [tenants, totalItems] = await this.prisma.$transaction([
      this.prisma.tenant.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.tenant.count({ where }),
    ])

    return {
      items: tenants,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    name,
    tenantUid,
    balance = 0,
  }: CreateTenantDto): Promise<TenantModel> {
    const existingTenant = await this.findByTenantUid(tenantUid)

    if (existingTenant) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'tenant',
          field: 'tenantUid',
        }),
      )
    }

    const tenant = await this.prisma.tenant.create({
      data: {
        name,
        tenantUid,
        balance,
      },
    })

    return tenant
  }

  public async update({
    id,
    name,
    tenantUid,
    balance,
  }: UpdateTenantDto): Promise<TenantModel> {
    await this.findById(id)

    const updateData: Record<string, unknown> = {}

    if (name !== undefined) updateData.name = name

    if (tenantUid !== undefined) {
      const tenantWithSameUid = await this.findByTenantUid(tenantUid)

      if (tenantWithSameUid && tenantWithSameUid.id !== id) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'tenant',
            field: 'tenantUid',
          }),
        )
      }

      updateData.tenantUid = tenantUid
    }

    if (balance !== undefined) updateData.balance = balance

    const tenant = await this.prisma.tenant.update({
      where: { id },
      data: updateData,
    })

    return tenant
  }

  public async delete(id: string): Promise<TenantModel> {
    await this.findById(id)

    const tenant = await this.prisma.tenant.update({
      where: { id },
      data: { deleted: true },
    })

    return tenant
  }
}
