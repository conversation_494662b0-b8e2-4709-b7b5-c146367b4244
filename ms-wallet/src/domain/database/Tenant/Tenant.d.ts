import type { Decimal } from '@prisma/client/runtime/library'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type TenantModel = {
  id: string
  name: string
  tenantUid: string
  balance: Decimal
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateTenantDto = {
  name: string
  tenantUid: string
  balance?: number
}

export type UpdateTenantDto = Partial<CreateTenantDto> & {
  id: string
}

export type FetchTenantsProps = PaginationParams & {
  id?: string
}
