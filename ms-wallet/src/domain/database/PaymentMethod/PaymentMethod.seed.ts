import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  const tenant = await prisma.tenant.findFirst()

  if (tenant) {
    await prisma.paymentMethod.upsert({
      where: { id: 'a1b2c3d4-e5f6-4a5b-9c8d-7e6f5a4b3c2d' },
      update: {},
      create: {
        id: 'a1b2c3d4-e5f6-4a5b-9c8d-7e6f5a4b3c2d',
        provider: 'stripe',
        config: {
          apiKey: 'sample_key',
          webhookSecret: 'sample_secret',
        },
        enabled: true,
        tenantId: tenant.id,
      },
    })
  } else {
    Debug.warn({
      message: 'No tenant found. Skipping payment method seed.',
    })
  }
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
