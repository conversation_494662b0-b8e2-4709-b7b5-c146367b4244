import type { Prisma } from '@prisma/client'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type PaymentMethodModel = {
  id: string
  tenantId: string
  provider: string
  config: Prisma.JsonValue
  enabled: boolean
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreatePaymentMethodDto = {
  tenantId: string
  provider: string
  config: string
  enabled?: boolean
}

export type UpdatePaymentMethodDto = Partial<CreatePaymentMethodDto> & {
  id: string
}

export type FetchPaymentMethodsProps = PaginationParams & {
  tenantId?: string
  provider?: string
  enabled?: boolean
}
