import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type DomainModel = {
  id: string
  tenantId: string
  name: string
  isPrimary: boolean
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateDomainDto = {
  tenantId: string
  name: string
  isPrimary?: boolean
}

export type UpdateDomainDto = Partial<CreateDomainDto> & {
  id: string
}

export type FetchDomainsProps = PaginationParams & {
  tenantId?: string
}
