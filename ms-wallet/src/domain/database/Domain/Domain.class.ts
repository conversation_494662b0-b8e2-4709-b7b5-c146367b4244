import { PrismaClient } from '@prisma/client'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateDomainDto,
  DomainModel,
  FetchDomainsProps,
  UpdateDomainDto,
} from '@app/domain/database/Domain'
import i18next from '@app/domain/locales/i18n/i18next'

export class Domain {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<DomainModel> {
    const domain = await this.prisma.domain.findFirst({
      where: { id, deleted: false },
    })

    if (!domain) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'domain' }),
      )
    }

    return domain
  }

  public async findByName(
    tenantId: string,
    name: string,
  ): Promise<DomainModel | null> {
    return this.prisma.domain.findFirst({
      where: { tenantId, name, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    tenantId,
  }: FetchDomainsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(tenantId && { tenantId }),
      ...(search && {
        OR: [{ name: { contains: search } }],
      }),
    }

    const [domains, totalItems] = await this.prisma.$transaction([
      this.prisma.domain.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.domain.count({ where }),
    ])

    return {
      items: domains,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    tenantId,
    name,
    isPrimary = false,
  }: CreateDomainDto): Promise<DomainModel> {
    // Check if domain with same name already exists for this tenant
    const existingDomain = await this.findByName(tenantId, name)

    if (existingDomain) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'domain',
          field: 'name',
        }),
      )
    }

    // If this is set as primary, we need to unset any existing primary domains
    if (isPrimary) {
      await this.prisma.domain.updateMany({
        where: { tenantId, isPrimary: true },
        data: { isPrimary: false },
      })
    }

    const domain = await this.prisma.domain.create({
      data: {
        tenant: {
          connect: { id: tenantId },
        },
        name,
        isPrimary,
      },
    })

    return domain
  }

  public async update({
    id,
    name,
    isPrimary,
  }: UpdateDomainDto): Promise<DomainModel> {
    // First check if domain exists
    const existingDomain = await this.findById(id)

    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (name !== undefined) {
      // Check if another domain with this name exists for the same tenant
      const domainWithSameName = await this.findByName(
        existingDomain.tenantId,
        name,
      )

      if (domainWithSameName && domainWithSameName.id !== id) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'domain',
            field: 'name',
          }),
        )
      }
      updateData.name = name
    }

    if (isPrimary !== undefined) {
      updateData.isPrimary = isPrimary

      // If setting as primary, unset any other primary domains for this tenant
      if (isPrimary) {
        await this.prisma.domain.updateMany({
          where: {
            tenantId: existingDomain.tenantId,
            isPrimary: true,
            id: { not: id },
          },
          data: { isPrimary: false },
        })
      }
    }

    const domain = await this.prisma.domain.update({
      where: { id },
      data: updateData,
    })

    return domain
  }

  public async delete(id: string): Promise<DomainModel> {
    // First check if domain exists
    await this.findById(id)

    const domain = await this.prisma.domain.update({
      where: { id },
      data: { deleted: true },
    })

    return domain
  }
}
