import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  const tenant = await prisma.tenant.findFirst()

  if (tenant) {
    await prisma.domain.upsert({
      where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479' },
      update: {},
      create: {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        name: 'thrift-dev.com',
        isPrimary: true,
        tenantId: tenant.id,
      },
    })
  } else {
    Debug.warn({
      message: 'No tenant found. Skipping domain seed.',
    })
  }
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
