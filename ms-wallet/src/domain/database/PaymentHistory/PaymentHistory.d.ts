import type { Decimal } from '@prisma/client/runtime/library'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type PaymentHistoryModel = {
  id: string
  tenantId: string
  paymentMethodId: string
  amount: Decimal
  currency: string
  status: string
  externalId?: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreatePaymentHistoryDto = {
  tenantId: string
  paymentMethodId: string
  amount: Decimal
  currency: string
  status: string
  externalId?: string
}

export type UpdatePaymentHistoryDto = Partial<
  Omit<CreatePaymentHistoryDto, 'tenantId'>
> & {
  id: string
}

export type FetchPaymentHistoriesProps = PaginationParams & {
  tenantId?: string
  paymentMethodId?: string
  status?: string
  fromDate?: Date
  toDate?: Date
}
