import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  const tenant = await prisma.tenant.findFirst()
  const paymentMethod = await prisma.paymentMethod.findFirst()

  if (tenant && paymentMethod) {
    await prisma.paymentHistory.upsert({
      where: { id: 'b5c67d89-e0f1-4a2b-8c9d-7e6f5a4b3c2d' },
      update: {},
      create: {
        id: 'b5c67d89-e0f1-4a2b-8c9d-7e6f5a4b3c2d',
        amount: '99.99',
        currency: 'BRL',
        status: 'completed',
        externalId: 'ch_123456789',
        tenantId: tenant.id,
        paymentMethodId: paymentMethod.id,
      },
    })
  } else {
    Debug.warn({
      message:
        'No tenant or payment method found. Skipping payment history seed.',
    })
  }
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
