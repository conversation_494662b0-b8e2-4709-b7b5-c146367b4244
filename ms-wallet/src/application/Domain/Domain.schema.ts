import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const DomainBaseSchema = z.object({
  tenantId: z.string().uuid(),
  name: z.string().min(3),
  isPrimary: z.boolean().optional(),
})

export const CreateDomainSchema = DomainBaseSchema

export const UpdateDomainSchema = DomainBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  name: true,
  isPrimary: true,
})

export const FilterDomainSchema = createFilterSchema({
  tenantId: z.string().uuid().optional(),
})
