import type { z } from 'zod'

import type {
  CreateDomainSchema,
  FilterDomainSchema,
  UpdateDomainSchema,
} from '@app/application/Domain/Domain.schema'

export type CreateDomainDto = z.infer<typeof CreateDomainSchema>
export type UpdateDomainDto = z.infer<typeof UpdateDomainSchema>
export type FilterDomainDto = z.infer<typeof FilterDomainSchema>

export type DomainData = {
  id: string
  tenantId: string
  name: string
  isPrimary: boolean
  createdAt: Date
  updatedAt: Date
}

export type FetchDomainsProps = FilterDomainDto
