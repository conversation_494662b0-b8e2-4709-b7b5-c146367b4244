import type { UpdateDomainDto } from '@app/application/Domain'
import { Language } from '@app/application/Language'

import { Domain } from '@app/domain/database/Domain'

export class Update {
  public async update({ id, name, isPrimary }: UpdateDomainDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'domain.id' }),
      )
    }

    if (name !== undefined && name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'domain.name',
        }),
      )
    }

    const model = new Domain()

    // Update the domain
    return model.update({
      id,
      name,
      isPrimary,
    })
  }
}
