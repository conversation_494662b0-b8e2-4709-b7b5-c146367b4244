import { validateWithZod } from '@thrift/common/engines/Validation'

import type { UpdateTenantDto } from '@app/application/Tenant'
import { UpdateTenantSchema } from '@app/application/Tenant/Tenant.schema'

import { Tenant } from '@app/domain/database/Tenant'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateTenantDto = validateWithZod(UpdateTenantSchema, input)

    const model = new Tenant()

    return model.update(dto)
  }
}
