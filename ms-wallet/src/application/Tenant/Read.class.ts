import {
  type FilterTenantDto,
  FilterTenantSchema,
} from '@app/application/Tenant'

import { Tenant } from '@app/domain/database/Tenant'

export class Read {
  public async findById(id: string) {
    if (!id) {
      throw new ReferenceError('Tenant ID is required')
    }

    const model = new Tenant()

    return model.findById(id)
  }

  public async find(input: unknown) {
    const filter: FilterTenantDto = FilterTenantSchema.parse(input)

    const model = new Tenant()

    return model.find(filter)
  }
}
