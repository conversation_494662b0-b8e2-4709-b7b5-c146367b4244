import type { z } from 'zod'

import type {
  CreateTenantSchema,
  FilterTenantSchema,
  UpdateTenantSchema,
} from '@app/application/Tenant'

export type CreateTenantDto = z.infer<typeof CreateTenantSchema>
export type UpdateTenantDto = z.infer<typeof UpdateTenantSchema>
export type FilterTenantDto = z.infer<typeof FilterTenantSchema>

export type FetchTenantsProps = FilterTenantDto

export type TenantData = {
  id: string
  name: string
  tenantUid: string
  balance: number
  createdAt: Date
  updatedAt: Date
}
