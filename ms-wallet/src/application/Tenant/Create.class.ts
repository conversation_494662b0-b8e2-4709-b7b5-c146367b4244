import { validateWithZod } from '@thrift/common/engines/Validation'

import type { CreateTenantDto } from '@app/application/Tenant'
import { CreateTenantSchema } from '@app/application/Tenant/Tenant.schema'

import { Tenant } from '@app/domain/database/Tenant'

export class Create {
  public async create(input: unknown) {
    const dto: CreateTenantDto = validateWithZod(CreateTenantSchema, input)

    const model = new Tenant()

    return model.create(dto)
  }
}
