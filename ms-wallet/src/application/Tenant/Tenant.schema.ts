import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const TenantBaseSchema = z.object({
  name: z.string().min(3),
  tenantUid: z.string().min(3),
  balance: z.number().nonnegative().optional(),
})

export const CreateTenantSchema = TenantBaseSchema
export const UpdateTenantSchema = TenantBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  name: true,
  tenantUid: true,
  balance: true,
})

export const FilterTenantSchema = createFilterSchema({
  name: z.string().optional(),
  tenantUid: z.string().optional(),
})
