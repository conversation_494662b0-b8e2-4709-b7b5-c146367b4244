import type { z } from 'zod'

import type {
  CreateTenantContactSchema,
  FilterTenantContactSchema,
  UpdateTenantContactSchema,
} from '@app/application/TenantContact/TenantContact.schema'

export type CreateTenantContactDto = z.infer<typeof CreateTenantContactSchema>
export type UpdateTenantContactDto = z.infer<typeof UpdateTenantContactSchema>
export type FilterTenantContactDto = z.infer<typeof FilterTenantContactSchema>

export type TenantContactData = {
  id: string
  tenantId: string
  type: string
  value: string
  isPrimary: boolean
  createdAt: Date
  updatedAt: Date
}

export type FetchTenantContactsProps = FilterTenantContactDto
