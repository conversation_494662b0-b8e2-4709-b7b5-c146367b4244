import { validateWith<PERSON>od } from '@thrift/common/engines/Validation'

import type { CreateTenantContactDto } from '@app/application/TenantContact'
import { CreateTenantContactSchema } from '@app/application/TenantContact/TenantContact.schema'

import { Tenant } from '@app/domain/database/Tenant'
import { TenantContact } from '@app/domain/database/TenantContact'

export class Create {
  public async create(input: unknown) {
    const dto: CreateTenantContactDto = validateWithZod(
      CreateTenantContactSchema,
      input,
    )

    const tenantModel = new Tenant()

    await tenantModel.findById(dto.tenantId)

    const model = new TenantContact()

    return model.create(dto)
  }
}
