import type { FetchTenantContactsProps } from '@app/application/TenantContact'

import { TenantContact } from '@app/domain/database/TenantContact'

export class Read {
  public async findById(id: string) {
    const model = new TenantContact()

    return model.findById(id)
  }

  public async find(params: FetchTenantContactsProps) {
    const model = new TenantContact()

    return model.find(params)
  }
}
