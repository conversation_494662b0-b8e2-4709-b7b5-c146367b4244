import { Language } from '@app/application/Language'
import type { UpdateTenantContactDto } from '@app/application/TenantContact'

import { TenantContact } from '@app/domain/database/TenantContact'

export class Update {
  public async update({ id, type, value, isPrimary }: UpdateTenantContactDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'tenant contact.id' }),
      )
    }

    const model = new TenantContact()

    return model.update({
      id,
      type,
      value,
      isPrimary,
    })
  }
}
