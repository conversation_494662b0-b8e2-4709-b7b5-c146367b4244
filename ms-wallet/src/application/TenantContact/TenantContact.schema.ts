import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const TenantContactBaseSchema = z.object({
  tenantId: z.string().uuid(),
  type: z.string().min(1),
  value: z.string().min(1),
  isPrimary: z.boolean().optional(),
})

export const CreateTenantContactSchema = TenantContactBaseSchema

export const UpdateTenantContactSchema = TenantContactBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  type: true,
  value: true,
  isPrimary: true,
})

export const FilterTenantContactSchema = createFilterSchema({
  tenantId: z.string().uuid().optional(),
})
