import type { FetchPaymentMethodsProps } from '@app/application/PaymentMethod'

import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Read {
  public async findById(id: string) {
    const model = new PaymentMethod()

    return model.findById(id)
  }

  public async find(params: FetchPaymentMethodsProps) {
    const model = new PaymentMethod()

    return model.find(params)
  }
}
