import { Language } from '@app/application/Language'
import type { UpdatePaymentMethodDto } from '@app/application/PaymentMethod'

import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Update {
  public async update({
    id,
    provider,
    config,
    enabled,
  }: UpdatePaymentMethodDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'payment method.id' }),
      )
    }

    if (provider !== undefined && provider.length < 2) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'payment method.provider',
        }),
      )
    }

    const model = new PaymentMethod()

    // Update the payment method
    return model.update({
      id,
      provider,
      config,
      enabled,
    })
  }
}
