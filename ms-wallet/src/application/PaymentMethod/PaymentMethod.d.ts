import type { z } from 'zod'

import type {
  CreatePaymentMethodSchema,
  FilterPaymentMethodSchema,
  UpdatePaymentMethodSchema,
} from '@app/application/PaymentMethod/PaymentMethod.schema'

export type CreatePaymentMethodDto = z.infer<typeof CreatePaymentMethodSchema>
export type UpdatePaymentMethodDto = z.infer<typeof UpdatePaymentMethodSchema>
export type FilterPaymentMethodDto = z.infer<typeof FilterPaymentMethodSchema>

export type PaymentMethodData = {
  id: string
  tenantId: string
  provider: string
  config: string
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

export type FetchPaymentMethodsProps = FilterPaymentMethodDto
