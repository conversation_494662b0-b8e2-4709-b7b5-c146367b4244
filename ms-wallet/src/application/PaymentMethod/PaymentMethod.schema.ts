import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const PaymentMethodBaseSchema = z.object({
  tenantId: z.string().uuid(),
  provider: z.string().min(2),
  config: z.string().min(1),
  enabled: z.boolean().optional(),
})

export const CreatePaymentMethodSchema = PaymentMethodBaseSchema

export const UpdatePaymentMethodSchema = PaymentMethodBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  provider: true,
  config: true,
  enabled: true,
})

export const FilterPaymentMethodSchema = createFilterSchema({
  tenantId: z.string().uuid().optional(),
  provider: z.string().optional(),
  enabled: z.boolean().optional(),
})
