import { CreatePaymentHistorySchema } from '@app/application/PaymentHistory'

import { PaymentHistory } from '@app/domain/database/PaymentHistory'
import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Create {
  public async create(input: unknown) {
    const dto = CreatePaymentHistorySchema.parse(input)

    // Validate that the payment method exists
    const paymentMethodModel = new PaymentMethod()

    await paymentMethodModel.findById(dto.paymentMethodId)

    const model = new PaymentHistory()

    // Create the payment history
    return model.create(dto)
  }
}
