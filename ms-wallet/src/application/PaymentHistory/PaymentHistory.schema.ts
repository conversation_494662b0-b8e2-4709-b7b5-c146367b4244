import { Decimal } from '@prisma/client/runtime/library'
import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const PaymentHistoryBaseSchema = z.object({
  tenantId: z.string().uuid(),
  paymentMethodId: z.string().uuid(),
  amount: z
    .number()
    .nonnegative()
    .transform((val) => new Decimal(val.toFixed(2))),
  currency: z.string().length(3),
  status: z.string().min(1),
  externalId: z.string().optional(),
})

export const CreatePaymentHistorySchema = PaymentHistoryBaseSchema

export const UpdatePaymentHistorySchema = PaymentHistoryBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  paymentMethodId: true,
  amount: true,
  currency: true,
  status: true,
  externalId: true,
})

export const FilterPaymentHistorySchema = createFilterSchema({
  tenantId: z.string().uuid().optional(),
  paymentMethodId: z.string().uuid().optional(),
  status: z.string().optional(),
  fromDate: z.coerce.date().optional(),
  toDate: z.coerce.date().optional(),
})
