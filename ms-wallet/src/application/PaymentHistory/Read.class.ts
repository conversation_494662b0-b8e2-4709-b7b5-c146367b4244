import type { FetchPaymentHistoriesProps } from '@app/application/PaymentHistory'

import { PaymentHistory } from '@app/domain/database/PaymentHistory'

export class Read {
  public async findById(id: string) {
    const model = new PaymentHistory()

    return model.findById(id)
  }

  public async find(params: FetchPaymentHistoriesProps) {
    const model = new PaymentHistory()

    return model.find(params)
  }
}
