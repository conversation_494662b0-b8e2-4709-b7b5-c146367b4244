import type { Decimal } from '@prisma/client/runtime/library'
import type { z } from 'zod'

import type {
  CreatePaymentHistorySchema,
  FilterPaymentHistorySchema,
  UpdatePaymentHistorySchema,
} from '@app/application/PaymentHistory/PaymentHistory.schema'

export type CreatePaymentHistoryDto = z.infer<typeof CreatePaymentHistorySchema>
export type UpdatePaymentHistoryDto = z.infer<typeof UpdatePaymentHistorySchema>
export type FilterPaymentHistoryDto = z.infer<typeof FilterPaymentHistorySchema>

export type PaymentHistoryData = {
  id: string
  tenantId: string
  paymentMethodId: string
  amount: Decimal
  currency: string
  status: string
  externalId?: string | null
  createdAt: Date
  updatedAt: Date
}

export type FetchPaymentHistoriesProps = FilterPaymentHistoryDto
