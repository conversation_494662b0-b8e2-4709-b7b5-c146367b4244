import { Language } from '@app/application/Language'
import type { UpdatePaymentHistoryDto } from '@app/application/PaymentHistory'

import { PaymentHistory } from '@app/domain/database/PaymentHistory'
import { PaymentMethod } from '@app/domain/database/PaymentMethod'

export class Update {
  public async update({
    id,
    paymentMethodId,
    amount,
    currency,
    status,
    externalId,
  }: UpdatePaymentHistoryDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'payment history.id' }),
      )
    }

    // Validate that amount is a positive number if provided
    if (amount && amount.lte(0)) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'payment history.amount',
        }),
      )
    }

    // Validate that currency is a valid 3-letter code if provided
    if (currency && currency.length !== 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'payment history.currency',
        }),
      )
    }

    // Validate that the payment method exists if provided
    if (paymentMethodId !== undefined) {
      const paymentMethodModel = new PaymentMethod()

      await paymentMethodModel.findById(paymentMethodId)
    }

    const model = new PaymentHistory()

    // Update the payment history
    return model.update({
      id,
      paymentMethodId,
      amount,
      currency,
      status,
      externalId,
    })
  }
}
