import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const TenantDocumentBaseSchema = z.object({
  tenantId: z.string().uuid(),
  type: z.string().min(1),
  value: z.string().min(1),
  issuedAt: z.date().nullable().optional(),
  expiresAt: z.date().nullable().optional(),
  isPrimary: z.boolean().optional(),
})

export const CreateTenantDocumentSchema = TenantDocumentBaseSchema

export const UpdateTenantDocumentSchema = TenantDocumentBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  type: true,
  value: true,
  issuedAt: true,
  expiresAt: true,
  isPrimary: true,
})

export const FilterTenantDocumentSchema = createFilterSchema({
  tenantId: z.string().uuid().optional(),
})
