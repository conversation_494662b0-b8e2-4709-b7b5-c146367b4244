import { Language } from '@app/application/Language'
import type { UpdateTenantDocumentDto } from '@app/application/TenantDocument'

import { TenantDocument } from '@app/domain/database/TenantDocument'

export class Update {
  public async update({
    id,
    type,
    value,
    issuedAt,
    expiresAt,
    isPrimary,
  }: UpdateTenantDocumentDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'tenant document.id' }),
      )
    }

    const model = new TenantDocument()

    return model.update({
      id,
      type,
      value,
      issuedAt,
      expiresAt,
      isPrimary,
    })
  }
}
