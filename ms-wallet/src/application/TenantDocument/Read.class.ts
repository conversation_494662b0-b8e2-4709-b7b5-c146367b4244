import type { FetchTenantDocumentsProps } from '@app/application/TenantDocument'

import { TenantDocument } from '@app/domain/database/TenantDocument'

export class Read {
  public async findById(id: string) {
    const model = new TenantDocument()

    return model.findById(id)
  }

  public async find(params: FetchTenantDocumentsProps) {
    const model = new TenantDocument()

    return model.find(params)
  }
}
