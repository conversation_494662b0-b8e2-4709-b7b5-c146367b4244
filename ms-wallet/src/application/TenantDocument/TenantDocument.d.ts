import type { z } from 'zod'

import type {
  CreateTenantDocumentSchema,
  FilterTenantDocumentSchema,
  UpdateTenantDocumentSchema,
} from '@app/application/TenantDocument/TenantDocument.schema'

export type CreateTenantDocumentDto = z.infer<typeof CreateTenantDocumentSchema>
export type UpdateTenantDocumentDto = z.infer<typeof UpdateTenantDocumentSchema>
export type FilterTenantDocumentDto = z.infer<typeof FilterTenantDocumentSchema>

export type TenantDocumentData = {
  id: string
  tenantId: string
  type: string
  value: string
  issuedAt?: Date | null
  expiresAt?: Date | null
  isPrimary: boolean
  createdAt: Date
  updatedAt: Date
}

export type FetchTenantDocumentsProps = FilterTenantDocumentDto
