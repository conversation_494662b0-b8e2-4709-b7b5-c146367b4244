import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import {
  Create,
  Delete as DeleteDomain,
  Read,
  Update,
  type UpdateDomainDto,
} from '@app/application/Domain'
import { Language } from '@app/application/Language'

import type {
  DomainFilterDto,
  IdentifierDto,
  PostDomainDto,
} from '@app/resources/Domain/Domain'

export class DomainResource {
  @Get('/domains/:id')
  public async getDomainById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'domain', Language.translate)
    }
  }

  @Get('/domains')
  public async getDomains({ request, response }: ResourceMethodProps) {
    try {
      const { tenantId } = request.query<DomainFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().find({
          page,
          perPage,
          search,
          sortBy,
          sortOrder,
          tenantId,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'domain', Language.translate)
    }
  }

  @Post('/domains')
  public async postDomain({ request, response }: ResourceMethodProps) {
    try {
      const postDomainDto = request.body<PostDomainDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'domain',
          },
        ),
        data: await new Create().create(postDomainDto),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'domain', Language.translate)
    }
  }

  @Put('/domains/:id')
  public async putDomain({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putDomainDto = request.body<UpdateDomainDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Update().update({
          ...putDomainDto,
          id,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'domain', Language.translate)
    }
  }

  @Delete('/domains/:id')
  public async deleteDomain({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new DeleteDomain().delete(id),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'domain', Language.translate)
    }
  }
}
