import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteTenantContact,
  Read,
  Update,
  type UpdateTenantContactDto,
} from '@app/application/TenantContact'

import type {
  IdentifierDto,
  PostTenantContactDto,
  TenantContactFilterDto,
} from '@app/resources/TenantContact/TenantContact'

export class TenantContactResource {
  @Get('/tenant-contacts/:id')
  public async getTenantContactById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new Read().findById(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant contact',
        Language.translate,
      )
    }
  }

  @Get('/tenant-contacts')
  public async getTenantContacts({ request, response }: ResourceMethodProps) {
    try {
      const { id, tenantId } = request.query<TenantContactFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const data = await new Read().find({
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        id,
        tenantId,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant contact',
        Language.translate,
      )
    }
  }

  @Post('/tenant-contacts')
  public async postTenantContact({ request, response }: ResourceMethodProps) {
    try {
      const postTenantContactDto = request.body<PostTenantContactDto>()

      const data = await new Create().create(postTenantContactDto)

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'tenant contact',
          },
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant contact',
        Language.translate,
      )
    }
  }

  @Put('/tenant-contacts/:id')
  public async putTenantContact({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putTenantContactDto = request.body<UpdateTenantContactDto>()

      const data = await new Update().update({
        ...putTenantContactDto,
        id,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant contact',
        Language.translate,
      )
    }
  }

  @Delete('/tenant-contacts/:id')
  public async deleteTenantContact({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new DeleteTenantContact().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant contact',
        Language.translate,
      )
    }
  }
}
