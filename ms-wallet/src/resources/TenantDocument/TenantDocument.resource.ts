import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteTenantDocument,
  Read,
  Update,
  type UpdateTenantDocumentDto,
} from '@app/application/TenantDocument'

import type {
  IdentifierDto,
  PostTenantDocumentDto,
  TenantDocumentFilterDto,
} from '@app/resources/TenantDocument/TenantDocument'

export class TenantDocumentResource {
  @Get('/tenant-documents/:id')
  public async getTenantDocumentById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new Read().findById(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant document',
        Language.translate,
      )
    }
  }

  @Get('/tenant-documents')
  public async getTenantDocuments({ request, response }: ResourceMethodProps) {
    try {
      const { id, tenantId } = request.query<TenantDocumentFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const data = await new Read().find({
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        id,
        tenantId,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant document',
        Language.translate,
      )
    }
  }

  @Post('/tenant-documents')
  public async postTenantDocument({ request, response }: ResourceMethodProps) {
    try {
      const postTenantDocumentDto = request.body<PostTenantDocumentDto>()

      // Convert date strings to Date objects if provided
      const issuedAt = postTenantDocumentDto.issuedAt
        ? new Date(postTenantDocumentDto.issuedAt)
        : null

      const expiresAt = postTenantDocumentDto.expiresAt
        ? new Date(postTenantDocumentDto.expiresAt)
        : null

      const data = await new Create().create({
        ...postTenantDocumentDto,
        issuedAt,
        expiresAt,
      })

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'tenant document',
          },
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant document',
        Language.translate,
      )
    }
  }

  @Put('/tenant-documents/:id')
  public async putTenantDocument({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putTenantDocumentDto = request.body<UpdateTenantDocumentDto>()

      // Convert date strings to Date objects if provided
      const issuedAt = putTenantDocumentDto.issuedAt
        ? new Date(putTenantDocumentDto.issuedAt)
        : undefined

      const expiresAt = putTenantDocumentDto.expiresAt
        ? new Date(putTenantDocumentDto.expiresAt)
        : undefined

      const data = await new Update().update({
        ...putTenantDocumentDto,
        id,
        issuedAt,
        expiresAt,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant document',
        Language.translate,
      )
    }
  }

  @Delete('/tenant-documents/:id')
  public async deleteTenantDocument({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new DeleteTenantDocument().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'tenant document',
        Language.translate,
      )
    }
  }
}
