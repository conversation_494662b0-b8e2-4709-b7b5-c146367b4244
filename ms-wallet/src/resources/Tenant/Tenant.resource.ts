import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  type CreateTenantDto,
  Delete as DeleteTenant,
  Read,
  Update,
  type UpdateTenantDto,
} from '@app/application/Tenant'

import type { IdentifierDto } from '@app/resources/Tenant/Tenant'

export class TenantResource {
  @Get('/tenants/:id')
  public async getTenantById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new Read().findById(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tenant', Language.translate)
    }
  }

  @Get('/tenants')
  public async getTenants({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.query<{ id?: string }>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const data = await new Read().find({
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        id,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tenant', Language.translate)
    }
  }

  @Post('/tenants')
  public async postTenant({ request, response }: ResourceMethodProps) {
    try {
      const postTenantDto = request.body<CreateTenantDto>()

      const data = await new Create().create(postTenantDto)

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'tenant',
          },
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tenant', Language.translate)
    }
  }

  @Put('/tenants/:id')
  public async putTenant({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putTenantDto = request.body<UpdateTenantDto>()

      const data = await new Update().update({
        ...putTenantDto,
        id,
      })

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tenant', Language.translate)
    }
  }

  @Delete('/tenants/:id')
  public async deleteTenant({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new DeleteTenant().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tenant', Language.translate)
    }
  }
}
