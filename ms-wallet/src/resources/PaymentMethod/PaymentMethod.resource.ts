import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeletePaymentMethod,
  Read,
  Update,
  type UpdatePaymentMethodDto,
} from '@app/application/PaymentMethod'

import type {
  IdentifierDto,
  PaymentMethodFilterDto,
  PostPaymentMethodDto,
} from '@app/resources/PaymentMethod/PaymentMethod'

export class PaymentMethodResource {
  @Get('/payment-methods/:id')
  public async getPaymentMethodById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new Read().findById(id)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment method',
        Language.translate,
      )
    }
  }

  @Get('/payment-methods')
  public async getPaymentMethods({ request, response }: ResourceMethodProps) {
    try {
      const { tenantId, provider, enabled } =
        request.query<PaymentMethodFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const data = await new Read().find({
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        tenantId,
        provider,
        enabled,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment method',
        Language.translate,
      )
    }
  }

  @Post('/payment-methods')
  public async postPaymentMethod({ request, response }: ResourceMethodProps) {
    try {
      const postPaymentMethodDto = request.body<PostPaymentMethodDto>()

      const data = await new Create().create(postPaymentMethodDto)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'payment method' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment method',
        Language.translate,
      )
    }
  }

  @Put('/payment-methods/:id')
  public async putPaymentMethod({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putPaymentMethodDto = request.body<UpdatePaymentMethodDto>()

      const data = await new Update().update({
        ...putPaymentMethodDto,
        id,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment method',
        Language.translate,
      )
    }
  }

  @Delete('/payment-methods/:id')
  public async deletePaymentMethod({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new DeletePaymentMethod().delete(id)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment method',
        Language.translate,
      )
    }
  }
}
