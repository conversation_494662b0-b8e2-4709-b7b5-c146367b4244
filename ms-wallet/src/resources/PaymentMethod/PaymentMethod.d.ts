export type IdentifierDto = {
  id: string
}

export type PaymentMethodIdentifierDto = {
  paymentMethodId: string
}

export type PostPaymentMethodDto = {
  tenantId: string
  provider: string
  config: string
  enabled?: boolean
}

export type PutPaymentMethodDto = {
  provider?: string
  config?: string
  enabled?: boolean
}

export type PaymentMethodFilterDto = {
  tenantId?: string
  provider?: string
  enabled?: boolean
}
