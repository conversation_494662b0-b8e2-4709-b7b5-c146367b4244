import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeletePaymentHistory,
  Read,
  Update,
  type UpdatePaymentHistoryDto,
} from '@app/application/PaymentHistory'

import type {
  IdentifierDto,
  PaymentHistoryFilterDto,
  PostPaymentHistoryDto,
} from '@app/resources/PaymentHistory/PaymentHistory'

export class PaymentHistoryResource {
  @Get('/payment-history/:id')
  public async getPaymentHistoryById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new Read().findById(id)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment history',
        Language.translate,
      )
    }
  }

  @Get('/payment-history')
  public async getPaymentHistories({ request, response }: ResourceMethodProps) {
    try {
      const { tenantId, paymentMethodId, status, fromDate, toDate } =
        request.query<PaymentHistoryFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      // Convert date strings to Date objects if provided
      const fromDateObj = fromDate ? new Date(fromDate) : undefined
      const toDateObj = toDate ? new Date(toDate) : undefined

      const data = await new Read().find({
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        tenantId,
        paymentMethodId,
        status,
        fromDate: fromDateObj,
        toDate: toDateObj,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment history',
        Language.translate,
      )
    }
  }

  @Post('/payment-history')
  public async postPaymentHistory({ request, response }: ResourceMethodProps) {
    try {
      const postPaymentHistoryDto = request.body<PostPaymentHistoryDto>()

      const data = await new Create().create(postPaymentHistoryDto)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'payment history' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment history',
        Language.translate,
      )
    }
  }

  @Put('/payment-history/:id')
  public async putPaymentHistory({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putPaymentHistoryDto = request.body<UpdatePaymentHistoryDto>()

      const data = await new Update().update({
        ...putPaymentHistoryDto,
        id,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment history',
        Language.translate,
      )
    }
  }

  @Delete('/payment-history/:id')
  public async deletePaymentHistory({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const data = await new DeletePaymentHistory().delete(id)

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'payment history',
        Language.translate,
      )
    }
  }
}
