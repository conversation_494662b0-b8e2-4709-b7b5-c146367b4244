import type { Decimal } from '@prisma/client/runtime/library'

export type IdentifierDto = {
  id: string
}

export type PaymentHistoryIdentifierDto = {
  paymentHistoryId: string
}

export type PostPaymentHistoryDto = {
  tenantId: string
  paymentMethodId: string
  amount: Decimal
  currency: string
  status: string
  externalId?: string
}

export type PutPaymentHistoryDto = {
  paymentMethodId?: string
  amount?: Decimal
  currency?: string
  status?: string
  externalId?: string
}

export type PaymentHistoryFilterDto = {
  tenantId?: string
  paymentMethodId?: string
  status?: string
  fromDate?: string
  toDate?: string
}
