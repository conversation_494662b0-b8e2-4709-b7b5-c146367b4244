# Thrift Wallet Application
Wallet application service

## About the Source (`src`) Package

My approach here (_Application Architecture_) was using the _[Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)_ as the base of the application, where:

- _**domain**_ package represents the **Enterprise Business Rules**;
- _**application**_ package represents the **Application Business Rules**;
- _**resources**_ package represents the **Application Business Rules**;
- _[app.config.json](app.config.sample.json) (copy/paste file)_ has the basic parameters to run the application. Just see the file; no doubt you will understand.

## About the Other Packages

- _[eslint.config.js](eslint.config.js)_ configuration to _[eslint](https://eslint.org)_;
- _**.vscode**_ workspace configuration to [vscode](https://code.visualstudio.com/docs/editor/workspaces#_singlefolder-workspace-settings).

## How to use this Application

### Github Personal Access Token
[Creating a personal access token (classic)](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic) in the [github](https://github.com/).
- **Note:** `personal-token-github`
- **Expiration:** `No expiration`
- **Select scopes:** `notifications`, `read:packages`, `repo`, and `workflow`

After that, inside in the directory repository, execute the command below:
```bash
$ export PERSONAL_TOKEN_GITHUB=<<personal-token-github>>
$ git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
```
*Replace the `<<personal-token-github>>` to your Github Personal Access Token*

### Docker
You will need [docker](https://docs.docker.com/engine/install) and [docker-compose](https://docs.docker.com/compose/install) to use the commands below. It's necessary to access the repository root path.

## Commands

Below some app commands.

### database

To set up the database service follow this page [@thrift/simulator#database](https://github.com/thrift-technology/simulator?tab=readme-ov-file#database)

### provisioning

[Provisioning](https://en.wikipedia.org/wiki/Provisioning_(technology)) is the process which involves the automated setup of infrastructure, including servers, networks, and storage, alongside the deployment and configuration of the macro/micro-services themselves, ensuring a scalable and secure environment."

```bash
$ docker-compose run --rm provisioning
```
