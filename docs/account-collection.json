{"info": {"_postman_id": "da2ed371-68eb-4513-a5b0-d3f12a2bfb03", "name": "Account", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "838766", "_collection_link": "https://thrift-5351.postman.co/workspace/Thrift~1923e171-4c70-4695-8154-a7fbf902f8ac/collection/838766-da2ed371-68eb-4513-a5b0-d3f12a2bfb03?action=share&source=collection_link&creator=838766"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "authenticate", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/authenticate", "host": ["{{baseUrl}}"], "path": ["authenticate"]}}, "response": []}, {"name": "authenticate", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.data && response.data.token) {", "    pm.environment.set(\"token\", response.data.token);", "    pm.environment.set(\"refreshToken\", response.data.refreshToken);", "    console.log(\"Token stored successfully\")", "} else {", "    console.log(\"Token not found!\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/authenticate", "host": ["{{baseUrl}}"], "path": ["authenticate"]}}, "response": []}, {"name": "authenticate/refresh", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/authenticate/refresh", "host": ["{{baseUrl}}"], "path": ["authenticate", "refresh"]}}, "response": []}, {"name": "authenticate refresh", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.data && response.data.token) {", "    pm.environment.set(\"token\", response.data.token);", "    console.log(\"Token stored successfully\")", "} else {", "    console.log(\"Token not found!\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refreshToken}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/authenticate/refresh", "host": ["{{baseUrl}}"], "path": ["authenticate", "refresh"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Role", "item": [{"name": "roles", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"key": "id", "value": "16799c36-4002-47a0-b72a-43bfbbe384e0"}]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"key": "id", "value": "c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e"}]}}, "response": []}, {"name": "roles", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testaang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "roles/:id/acls", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"aclIds\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles/:id/acls", "host": ["{{baseUrl}}"], "path": ["roles", ":id", "acls"], "variable": [{"key": "id", "value": "c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Acl", "item": [{"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "acl/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "acl/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Account", "item": [{"name": "account", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "accounts", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"123456\",\n    \"password\": \"1234\",\n    \"roleId\": \"c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e\",\n    \"name\": \"<PERSON>\",\n    \"birthDate\": \"1990-01-01\",\n    \"gender\": \"M\",\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"123456\",\n    \"password\": \"1234\",\n    \"roleId\": \"c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e\",\n    \"name\": \"<PERSON>\",\n    \"birthDate\": \"1990-01-01\",\n    \"gender\": \"M\",\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:id/activate", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id/activate", "host": ["{{baseUrl}}"], "path": ["accounts", ":id", "activate"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:id/deactivate", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id/deactivate", "host": ["{{baseUrl}}"], "path": ["accounts", ":id", "deactivate"], "variable": [{"key": "id", "value": "9951972e-3413-48a8-80b7-731f10899d94"}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}