{"info": {"_postman_id": "", "name": "Person", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "838766", "_collection_link": "https://thrift-5351.postman.co/workspace/Thrift~1923e171-4c70-4695-8154-a7fbf902f8ac/collection/838766-person-collection"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8081", "type": "string"}], "item": [{"name": "Person", "item": [{"name": "/person", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person", "host": ["{{baseUrl}}"], "path": ["person"]}}, "response": []}, {"name": "/person/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/:personId", "host": ["{{baseUrl}}"], "path": ["person", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/person", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person", "host": ["{{baseUrl}}"], "path": ["person"]}}, "response": []}, {"name": "/person/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/:personId", "host": ["{{baseUrl}}"], "path": ["person", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}, {"name": "Person Natural", "item": [{"name": "/person/natural", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/natural", "host": ["{{baseUrl}}"], "path": ["person", "natural"]}}, "response": []}, {"name": "/person/natural/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/:id", "host": ["{{baseUrl}}"], "path": ["person", "natural", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/natural/personId/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person", "natural", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/person/natural", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/person/natural", "host": ["{{baseUrl}}"], "path": ["person", "natural"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"nickname\": \"<PERSON>\",\n    \"birthDate\": \"1990-01-01\",\n    \"gender\": \"male\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/person/natural/:personId", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/:personId", "host": ["{{baseUrl}}"], "path": ["person", "natural", ":personId"], "variable": [{"key": "personId", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"nickname\": \"<PERSON>\",\n    \"birthDate\": \"1990-01-01\",\n    \"gender\": \"male\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/person/natural/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/:id", "host": ["{{baseUrl}}"], "path": ["person", "natural", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/natural", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/natural", "host": ["{{baseUrl}}"], "path": ["person", "natural"]}}, "response": []}, {"name": "/person/natural/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/:id", "host": ["{{baseUrl}}"], "path": ["person", "natural", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/natural/personId/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/natural/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person", "natural", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}, {"name": "Person Legal", "item": [{"name": "/person/legal", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/legal", "host": ["{{baseUrl}}"], "path": ["person", "legal"]}}, "response": []}, {"name": "/person/legal/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:id", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/legal/personId/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person", "legal", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/person/legal", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/person/legal", "host": ["{{baseUrl}}"], "path": ["person", "legal"]}, "body": {"mode": "raw", "raw": "{\n    \"nickname\": \"Company Inc\",\n    \"fictitiousName\": \"Company Fictitious Name\",\n    \"legalName\": \"Company Legal Name Inc.\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/person/legal/:personId", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:personId", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":personId"], "variable": [{"key": "personId", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"nickname\": \"Updated Company\",\n    \"fictitiousName\": \"Updated Fictitious Name\",\n    \"legalName\": \"Updated Legal Name Inc.\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/person/legal/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:id", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/legal", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal", "host": ["{{baseUrl}}"], "path": ["person", "legal"]}}, "response": []}, {"name": "/person/legal/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:id", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person/legal/personId/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person", "legal", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}, {"name": "Contact", "item": [{"name": "/contact", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}}, "response": []}, {"name": "/contact/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/contact/personId/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact/personId/:personId", "host": ["{{baseUrl}}"], "path": ["contact", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/contact", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}, "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567890\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/contact/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+0987654321\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/contact/:personId", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/contact/:personId", "host": ["{{baseUrl}}"], "path": ["contact", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/contact", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}}, "response": []}, {"name": "/contact/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/contact/personId/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact/personId/:personId", "host": ["{{baseUrl}}"], "path": ["contact", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}, {"name": "Address", "item": [{"name": "/address", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}}, "response": []}, {"name": "/address/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/address/personId/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address/personId/:personId", "host": ["{{baseUrl}}"], "path": ["address", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/address", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}, "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"street\": \"123 Main St\",\n    \"city\": \"Anytown\",\n    \"state\": \"ST\",\n    \"zipCode\": \"12345\",\n    \"country\": \"Country\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/address/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"street\": \"456 New St\",\n    \"city\": \"Newtown\",\n    \"state\": \"NS\",\n    \"zipCode\": \"54321\",\n    \"country\": \"New Country\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/address/:personId", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/address/:personId", "host": ["{{baseUrl}}"], "path": ["address", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/address", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}}, "response": []}, {"name": "/address/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/address/personId/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address/personId/:personId", "host": ["{{baseUrl}}"], "path": ["address", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}, {"name": "Document", "item": [{"name": "/document", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}}, "response": []}, {"name": "/document/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/document/personId/:personId", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document/personId/:personId", "host": ["{{baseUrl}}"], "path": ["document", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/document", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}, "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"type\": \"ID\",\n    \"number\": \"123456789\",\n    \"issuedAt\": \"2020-01-01\",\n    \"expiresAt\": \"2030-01-01\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/document/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"type\": \"ID\",\n    \"number\": \"987654321\",\n    \"issuedAt\": \"2020-01-01\",\n    \"expiresAt\": \"2030-01-01\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/document/:personId", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/document/:personId", "host": ["{{baseUrl}}"], "path": ["document", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/document", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}}, "response": []}, {"name": "/document/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/document/personId/:personId", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document/personId/:personId", "host": ["{{baseUrl}}"], "path": ["document", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}