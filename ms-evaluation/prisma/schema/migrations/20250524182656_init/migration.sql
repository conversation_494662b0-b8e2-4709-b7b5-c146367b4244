-- CreateTable
CREATE TABLE `evaluation_batches` (
    `id` VARCHAR(191) NOT NULL,
    `supplierId` VARCHAR(191) NOT NULL,
    `storeId` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `displayCode` VARCHAR(191) NOT NULL,
    `notes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deleted` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `evaluation_items` (
    `id` VARCHAR(191) NOT NULL,
    `evaluationBatchId` VARCHAR(191) NOT NULL,
    `supplierDescription` VARCHAR(191) NULL,
    `supplierSuggestedCategoryId` VARCHAR(191) NULL,
    `evaluatedProductCategoryId` VARCHAR(191) NOT NULL,
    `evaluatedProductClassificationId` VARCHAR(191) NOT NULL,
    `evaluatorNotes` VARCHAR(191) NULL,
    `proposedBuyPrice` DECIMAL(65, 30) NOT NULL DEFAULT 0,
    `status` VARCHAR(191) NOT NULL,
    `rejectionReason` VARCHAR(191) NULL,
    `estimatedMarketValue` DECIMAL(65, 30) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deleted` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `evaluation_proposals` (
    `id` VARCHAR(191) NOT NULL,
    `evaluationBatchId` VARCHAR(191) NOT NULL,
    `totalProposedValue` DECIMAL(65, 30) NOT NULL DEFAULT 0,
    `proposalDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `status` VARCHAR(191) NOT NULL,
    `supplierResponseDate` DATETIME(3) NULL,
    `supplierNotes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deleted` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `evaluation_items` ADD CONSTRAINT `evaluation_items_evaluationBatchId_fkey` FOREIGN KEY (`evaluationBatchId`) REFERENCES `evaluation_batches`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `evaluation_proposals` ADD CONSTRAINT `evaluation_proposals_evaluationBatchId_fkey` FOREIGN KEY (`evaluationBatchId`) REFERENCES `evaluation_batches`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
