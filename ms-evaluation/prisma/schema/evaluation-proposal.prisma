model EvaluationProposal {
  id                    String          @id @default(uuid())
  evaluationBatch       EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId     String
  totalProposedValue    Decimal         @default(0)
  proposalDate          DateTime        @default(now())
  status                String          // DRAFT, SENT_TO_SUPPLIER, ACCEPTED_BY_SUPPLIER, REJECTED_BY_SUPPLIER
  supplierResponseDate  DateTime?      
  supplierNotes         String?        
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  deleted               Boolean         @default(false)

  @@map("evaluation_proposals")
}
