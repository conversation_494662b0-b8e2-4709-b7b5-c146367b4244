model EvaluationBatch {
  id          String   @id @default(uuid())
  supplierId  String   // FK to Person.id from ms-person
  storeId     String   // FK to Store.id or TenantStore.id from TBD
  status      String   // PENDING_REVIEW, UNDER_EVALUATION, EVALUATION_COMPLETED, PROPOSAL_SENT, PROPOSAL_ACCEPTED, PROPOSAL_REJECTED, CLOSED
  displayCode String   // Code for totem
  notes       String? 
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  items     EvaluationItem[]
  proposals EvaluationProposal[]

  @@map("evaluation_batches")
}