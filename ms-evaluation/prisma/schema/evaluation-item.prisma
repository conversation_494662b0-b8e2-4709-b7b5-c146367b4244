model EvaluationItem {
  id                                String          @id @default(uuid())
  evaluationBatch                   EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId                 String
  supplierDescription               String?         
  supplierSuggestedCategoryId       String?         
  evaluatedProductCategoryId        String          // FK to ProductCategory from ms-stock
  evaluatedProductClassificationId  String          // FK to ProductClassification from ms-stock
  evaluatorNotes                    String?
  proposedBuyPrice                  Decimal         @default(0)
  status                            String          // PENDING_EVALUATION, ACCEPTED, REJECTED
  rejectionReason                   String?
  estimatedMarketValue              Decimal?
  createdAt                         DateTime        @default(now())
  updatedAt                         DateTime        @updatedAt
  deleted                           Boolean         @default(false)

  @@map("evaluation_items")
}