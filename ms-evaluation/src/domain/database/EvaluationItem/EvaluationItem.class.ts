import { PrismaClient } from '@prisma/client'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateEvaluationItemDto,
  EvaluationItemModel,
  FetchEvaluationItemsProps,
  UpdateEvaluationItemDto,
} from '@app/domain/database/EvaluationItem'
import i18next from '@app/domain/locales/i18n/i18next'

export class EvaluationItem {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<EvaluationItemModel> {
    const evaluationItem = await this.prisma.evaluationItem.findFirst({
      where: { id, deleted: false },
    })

    if (!evaluationItem) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'evaluation item' }),
      )
    }

    return evaluationItem
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    evaluationBatchId,
    status,
    evaluatedProductCategoryId,
    evaluatedProductClassificationId,
  }: FetchEvaluationItemsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(evaluationBatchId && { evaluationBatchId }),
      ...(status && { status }),
      ...(evaluatedProductCategoryId && { evaluatedProductCategoryId }),
      ...(evaluatedProductClassificationId && {
        evaluatedProductClassificationId,
      }),
      ...(search && {
        OR: [
          { supplierDescription: { contains: search } },
          { evaluatorNotes: { contains: search } },
          { status: { contains: search } },
          { rejectionReason: { contains: search } },
        ],
      }),
    }

    const [evaluationItems, totalItems] = await this.prisma.$transaction([
      this.prisma.evaluationItem.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        include: {
          evaluationBatch: {
            select: {
              id: true,
              displayCode: true,
              status: true,
            },
          },
        },
      }),
      this.prisma.evaluationItem.count({ where }),
    ])

    return {
      items: evaluationItems,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    evaluationBatchId,
    supplierDescription,
    supplierSuggestedCategoryId,
    evaluatedProductCategoryId,
    evaluatedProductClassificationId,
    evaluatorNotes,
    proposedBuyPrice,
    status,
    rejectionReason,
    estimatedMarketValue,
  }: CreateEvaluationItemDto): Promise<EvaluationItemModel> {
    const evaluationItem = await this.prisma.evaluationItem.create({
      data: {
        evaluationBatch: {
          connect: { id: evaluationBatchId },
        },
        supplierDescription,
        supplierSuggestedCategoryId,
        evaluatedProductCategoryId,
        evaluatedProductClassificationId,
        evaluatorNotes,
        proposedBuyPrice,
        status,
        rejectionReason,
        estimatedMarketValue,
      },
    })

    return evaluationItem
  }

  public async update({
    id,
    supplierDescription,
    supplierSuggestedCategoryId,
    evaluatedProductCategoryId,
    evaluatedProductClassificationId,
    evaluatorNotes,
    proposedBuyPrice,
    status,
    rejectionReason,
    estimatedMarketValue,
  }: UpdateEvaluationItemDto): Promise<EvaluationItemModel> {
    // First check if evaluation item exists
    await this.findById(id)

    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (supplierDescription !== undefined)
      updateData.supplierDescription = supplierDescription
    if (supplierSuggestedCategoryId !== undefined)
      updateData.supplierSuggestedCategoryId = supplierSuggestedCategoryId
    if (evaluatedProductCategoryId !== undefined)
      updateData.evaluatedProductCategoryId = evaluatedProductCategoryId
    if (evaluatedProductClassificationId !== undefined)
      updateData.evaluatedProductClassificationId =
        evaluatedProductClassificationId
    if (evaluatorNotes !== undefined) updateData.evaluatorNotes = evaluatorNotes
    if (proposedBuyPrice !== undefined)
      updateData.proposedBuyPrice = proposedBuyPrice
    if (status !== undefined) updateData.status = status
    if (rejectionReason !== undefined)
      updateData.rejectionReason = rejectionReason
    if (estimatedMarketValue !== undefined)
      updateData.estimatedMarketValue = estimatedMarketValue

    const evaluationItem = await this.prisma.evaluationItem.update({
      where: { id },
      data: updateData,
    })

    return evaluationItem
  }

  public async delete(id: string): Promise<EvaluationItemModel> {
    // First check if evaluation item exists
    await this.findById(id)

    const evaluationItem = await this.prisma.evaluationItem.update({
      where: { id },
      data: { deleted: true },
    })

    return evaluationItem
  }

  public async findByBatchId(
    evaluationBatchId: string,
  ): Promise<EvaluationItemModel[]> {
    return this.prisma.evaluationItem.findMany({
      where: { evaluationBatchId, deleted: false },
      orderBy: { createdAt: 'asc' },
    })
  }

  public async countByBatchAndStatus(
    evaluationBatchId: string,
    status: string,
  ): Promise<number> {
    return this.prisma.evaluationItem.count({
      where: { evaluationBatchId, status, deleted: false },
    })
  }
}
