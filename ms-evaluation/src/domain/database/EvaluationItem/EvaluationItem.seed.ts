import { PrismaClient } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  // Create sample evaluation items for the first batch
  await prisma.evaluationItem.upsert({
    where: { id: 'ei-001-uuid-sample-item-001' },
    update: {},
    create: {
      id: 'ei-001-uuid-sample-item-001',
      evaluationBatchId: 'eb-001-uuid-sample-batch-001',
      supplierDescription: 'Vintage leather jacket in good condition',
      supplierSuggestedCategoryId: 'cat-clothing-001',
      evaluatedProductCategoryId: 'cat-clothing-001',
      evaluatedProductClassificationId: 'class-vintage-001',
      evaluatorNotes: 'Good quality leather, minor wear on sleeves',
      proposedBuyPrice: new Decimal('45.00'),
      status: 'PENDING_EVALUATION',
      estimatedMarketValue: new Decimal('120.00'),
    },
  })

  await prisma.evaluationItem.upsert({
    where: { id: 'ei-002-uuid-sample-item-002' },
    update: {},
    create: {
      id: 'ei-002-uuid-sample-item-002',
      evaluationBatchId: 'eb-001-uuid-sample-batch-001',
      supplierDescription: 'Designer handbag with original tags',
      supplierSuggestedCategoryId: 'cat-accessories-001',
      evaluatedProductCategoryId: 'cat-accessories-001',
      evaluatedProductClassificationId: 'class-designer-001',
      evaluatorNotes: 'Excellent condition, authentic designer piece',
      proposedBuyPrice: new Decimal('85.00'),
      status: 'ACCEPTED',
      estimatedMarketValue: new Decimal('250.00'),
    },
  })

  await prisma.evaluationItem.upsert({
    where: { id: 'ei-003-uuid-sample-item-003' },
    update: {},
    create: {
      id: 'ei-003-uuid-sample-item-003',
      evaluationBatchId: 'eb-002-uuid-sample-batch-002',
      supplierDescription: 'Worn out sneakers',
      supplierSuggestedCategoryId: 'cat-shoes-001',
      evaluatedProductCategoryId: 'cat-shoes-001',
      evaluatedProductClassificationId: 'class-casual-001',
      evaluatorNotes: 'Too much wear, not suitable for resale',
      proposedBuyPrice: new Decimal('0.00'),
      status: 'REJECTED',
      rejectionReason: 'Excessive wear and tear',
      estimatedMarketValue: new Decimal('5.00'),
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
