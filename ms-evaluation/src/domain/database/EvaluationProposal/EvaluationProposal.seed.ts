import { PrismaClient } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  // Create sample evaluation proposals
  await prisma.evaluationProposal.upsert({
    where: { id: 'ep-001-uuid-sample-proposal-001' },
    update: {},
    create: {
      id: 'ep-001-uuid-sample-proposal-001',
      evaluationBatchId: 'eb-001-uuid-sample-batch-001',
      totalProposedValue: new Decimal('130.00'),
      proposalDate: new Date('2024-01-15'),
      status: 'DRAFT',
      supplierNotes: null,
    },
  })

  await prisma.evaluationProposal.upsert({
    where: { id: 'ep-002-uuid-sample-proposal-002' },
    update: {},
    create: {
      id: 'ep-002-uuid-sample-proposal-002',
      evaluationBatchId: 'eb-002-uuid-sample-batch-002',
      totalProposedValue: new Decimal('85.00'),
      proposalDate: new Date('2024-01-20'),
      status: 'SENT_TO_SUPPLIER',
      supplierNotes: null,
    },
  })

  await prisma.evaluationProposal.upsert({
    where: { id: 'ep-003-uuid-sample-proposal-003' },
    update: {},
    create: {
      id: 'ep-003-uuid-sample-proposal-003',
      evaluationBatchId: 'eb-003-uuid-sample-batch-003',
      totalProposedValue: new Decimal('200.00'),
      proposalDate: new Date('2024-01-25'),
      status: 'ACCEPTED_BY_SUPPLIER',
      supplierResponseDate: new Date('2024-01-27'),
      supplierNotes: 'Agreed to the proposed values. Ready for pickup.',
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
