import type { Decimal } from '@prisma/client/runtime/library'

import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type EvaluationProposalModel = {
  id: string
  evaluationBatchId: string
  totalProposedValue: Decimal
  proposalDate: Date
  status: string
  supplierResponseDate?: Date | null
  supplierNotes?: string | null
  supplierPaymentPreference: string
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateEvaluationProposalDto = {
  evaluationBatchId: string
  totalProposedValue: Decimal
  proposalDate?: Date
  status: string
  supplierResponseDate?: Date
  supplierNotes?: string
  supplierPaymentPreference?: string
}

export type UpdateEvaluationProposalDto = Partial<
  Omit<CreateEvaluationProposalDto, 'evaluationBatchId'>
> & {
  id: string
}

export type RespondToProposalDto = {
  id: string
  status: string
  supplierPaymentPreference?: string
  supplierNotes?: string
}

export type FetchEvaluationProposalsProps = PaginationParams & {
  evaluationBatchId?: string
  status?: string
  fromDate?: Date
  toDate?: Date
}
