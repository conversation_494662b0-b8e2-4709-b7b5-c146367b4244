import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type EvaluationBatchModel = {
  id: string
  supplierId: string
  storeId: string
  status: string
  displayCode: string
  notes?: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type CreateEvaluationBatchDto = {
  supplierId: string
  storeId: string
  status: string
  displayCode: string
  notes?: string
}

export type UpdateEvaluationBatchDto = Partial<CreateEvaluationBatchDto> & {
  id: string
}

export type FetchEvaluationBatchesProps = PaginationParams & {
  supplierId?: string
  storeId?: string
  status?: string
  displayCode?: string
}
