import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  // Create sample evaluation batches
  await prisma.evaluationBatch.upsert({
    where: { id: 'eb-001-uuid-sample-batch-001' },
    update: {},
    create: {
      id: 'eb-001-uuid-sample-batch-001',
      supplierId: 'supplier-001-uuid-sample',
      storeId: 'store-001-uuid-sample',
      status: 'PENDING_REVIEW',
      displayCode: 'EB001',
      notes: 'First evaluation batch for testing',
    },
  })

  await prisma.evaluationBatch.upsert({
    where: { id: 'eb-002-uuid-sample-batch-002' },
    update: {},
    create: {
      id: 'eb-002-uuid-sample-batch-002',
      supplierId: 'supplier-002-uuid-sample',
      storeId: 'store-001-uuid-sample',
      status: 'UNDER_EVALUATION',
      displayCode: 'EB002',
      notes: 'Second evaluation batch for testing',
    },
  })

  await prisma.evaluationBatch.upsert({
    where: { id: 'eb-003-uuid-sample-batch-003' },
    update: {},
    create: {
      id: 'eb-003-uuid-sample-batch-003',
      supplierId: 'supplier-001-uuid-sample',
      storeId: 'store-002-uuid-sample',
      status: 'EVALUATION_COMPLETED',
      displayCode: 'EB003',
      notes: 'Third evaluation batch for testing',
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
