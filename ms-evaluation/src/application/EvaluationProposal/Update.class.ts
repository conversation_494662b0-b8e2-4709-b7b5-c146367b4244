import type { UpdateEvaluationProposalDto } from '@app/application/EvaluationProposal'
import { UpdateEvaluationProposalSchema } from '@app/application/EvaluationProposal/EvaluationProposal.schema'

import { EvaluationProposal } from '@app/domain/database/EvaluationProposal'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateEvaluationProposalDto =
      UpdateEvaluationProposalSchema.parse(input)

    const model = new EvaluationProposal()

    return model.update(dto)
  }
}
