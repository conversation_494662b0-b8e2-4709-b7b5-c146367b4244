import { Decimal } from '@prisma/client/runtime/library'
import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination/PaginationSchema.schema'

export const EvaluationProposalBaseSchema = z.object({
  evaluationBatchId: z.string().uuid(),
  totalProposedValue: z
    .number()
    .nonnegative()
    .transform((val) => new Decimal(val.toFixed(2))),
  proposalDate: z.date().optional(),
  status: z.enum([
    'DRAFT',
    'SENT_TO_SUPPLIER',
    'ACCEPTED_BY_SUPPLIER',
    'REJECTED_BY_SUPPLIER',
  ]),
  supplierResponseDate: z.date().optional(),
  supplierNotes: z.string().optional(),
})

export const CreateEvaluationProposalSchema = EvaluationProposalBaseSchema

export const UpdateEvaluationProposalSchema =
  EvaluationProposalBaseSchema.extend({
    id: z.string().uuid(),
  }).partial({
    evaluationBatchId: true,
    totalProposedValue: true,
    proposalDate: true,
    status: true,
    supplierResponseDate: true,
    supplierNotes: true,
  })

export const FilterEvaluationProposalSchema = createFilterSchema({
  evaluationBatchId: z.string().uuid().optional(),
  status: z
    .enum([
      'DRAFT',
      'SENT_TO_SUPPLIER',
      'ACCEPTED_BY_SUPPLIER',
      'REJECTED_BY_SUPPLIER',
    ])
    .optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
})
