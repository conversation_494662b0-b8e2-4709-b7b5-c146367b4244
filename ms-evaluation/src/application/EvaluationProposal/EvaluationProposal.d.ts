import type { z } from 'zod'

import type {
  CreateEvaluationProposalSchema,
  FilterEvaluationProposalSchema,
  UpdateEvaluationProposalSchema,
} from '@app/application/EvaluationProposal/EvaluationProposal.schema'

export type CreateEvaluationProposalDto = z.infer<
  typeof CreateEvaluationProposalSchema
>
export type UpdateEvaluationProposalDto = z.infer<
  typeof UpdateEvaluationProposalSchema
>
export type FilterEvaluationProposalDto = z.infer<
  typeof FilterEvaluationProposalSchema
>

export type FetchEvaluationProposalsProps = FilterEvaluationProposalDto

export type EvaluationProposalData = {
  id: string
  evaluationBatchId: string
  totalProposedValue: number
  proposalDate: Date
  status: string
  supplierResponseDate?: Date | null
  supplierNotes?: string | null
  createdAt: Date
  updatedAt: Date
}
