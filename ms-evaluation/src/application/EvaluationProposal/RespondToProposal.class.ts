import type { RespondToProposalDto } from '@app/application/EvaluationProposal'
import { RespondToProposalSchema } from '@app/application/EvaluationProposal/EvaluationProposal.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'
import { EvaluationProposal } from '@app/domain/database/EvaluationProposal'
import { personServiceClient, purchaseServiceClient } from '@app/config/microservices'

export class RespondToProposal {
  public async respond(input: unknown) {
    const dto: RespondToProposalDto = RespondToProposalSchema.parse(input)

    const proposalModel = new EvaluationProposal()

    // Find the existing proposal
    const existingProposal = await proposalModel.findById(dto.id)

    // Update the proposal with supplier response
    const updatedProposal = await proposalModel.update({
      id: dto.id,
      status: dto.status,
      supplierResponseDate: new Date(),
      supplierNotes: dto.supplierNotes,
      supplierPaymentPreference: dto.supplierPaymentPreference,
    })

    // If proposal is accepted, handle inter-service calls
    if (dto.status === 'ACCEPTED_BY_SUPPLIER') {
      await this.handleAcceptedProposal(existingProposal, dto)
    }

    return updatedProposal
  }

  private async handleAcceptedProposal(
    proposal: any,
    dto: RespondToProposalDto,
  ) {
    // Get evaluation batch to access storeId and supplierId
    const batchModel = new EvaluationBatch()
    const batch = await batchModel.findById(proposal.evaluationBatchId)

    // TODO: Call ms-purchase to record purchase
    await this.recordPurchase({
      amount: proposal.totalProposedValue,
      supplierId: batch.supplierId,
      storeId: batch.storeId,
      proposalId: proposal.id,
    })

    // If store credit is selected, call ms-person to create store credit
    if (dto.supplierPaymentPreference === 'STORE_CREDIT') {
      await this.createStoreCredit({
        personId: batch.supplierId,
        amount: proposal.totalProposedValue,
        issuingStoreLegalPersonId: batch.storeId, // Assuming storeId maps to PersonLegal ID
        originatingProposalId: proposal.id,
      })
    }
  }

  private async recordPurchase(data: {
    amount: any
    supplierId: string
    storeId: string
    proposalId: string
  }) {
    try {
      // TODO: Implement call to ms-purchase when the service is ready
      // For now, we'll just log the purchase data
      console.log('Recording purchase:', {
        amount: Number(data.amount),
        supplierId: data.supplierId,
        storeId: data.storeId,
        proposalId: data.proposalId,
      })

      // When ms-purchase is ready, uncomment this:
      // await purchaseServiceClient.post('/purchases', {
      //   amount: Number(data.amount),
      //   supplierId: data.supplierId,
      //   storeId: data.storeId,
      //   proposalId: data.proposalId,
      // })
    } catch (error) {
      console.error('Failed to record purchase:', error)
      // In a production environment, you might want to handle this differently
      // For now, we'll continue with the process
    }
  }

  private async createStoreCredit(data: {
    personId: string
    amount: any
    issuingStoreLegalPersonId: string
    originatingProposalId: string
  }) {
    try {
      const response = await personServiceClient.post(
        `/persons/${data.personId}/store-credits`,
        {
          balance: Number(data.amount),
          currency: 'BRL',
          issuingStoreLegalPersonId: data.issuingStoreLegalPersonId,
          originatingProposalId: data.originatingProposalId,
          notes: 'Store credit from accepted evaluation proposal',
        },
      )

      console.log('Store credit created successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to create store credit:', error)
      throw new Error('Failed to create store credit for supplier')
    }
  }
}
