import type { CreateEvaluationBatchDto } from '@app/application/EvaluationBatch'
import { CreateEvaluationBatchSchema } from '@app/application/EvaluationBatch/EvaluationBatch.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'

export class Create {
  public async create(input: unknown) {
    const dto: CreateEvaluationBatchDto =
      CreateEvaluationBatchSchema.parse(input)

    const model = new EvaluationBatch()

    // Create the evaluation batch
    return model.create(dto)
  }
}
