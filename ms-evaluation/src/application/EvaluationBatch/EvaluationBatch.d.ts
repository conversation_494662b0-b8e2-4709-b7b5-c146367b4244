import type { z } from 'zod'

import type {
  CreateEvaluationBatchSchema,
  FilterEvaluationBatchSchema,
  UpdateEvaluationBatchSchema,
} from '@app/application/EvaluationBatch/EvaluationBatch.schema'

export type CreateEvaluationBatchDto = z.infer<
  typeof CreateEvaluationBatchSchema
>
export type UpdateEvaluationBatchDto = z.infer<
  typeof UpdateEvaluationBatchSchema
>
export type FilterEvaluationBatchDto = z.infer<
  typeof FilterEvaluationBatchSchema
>

export type FetchEvaluationBatchesProps = FilterEvaluationBatchDto

export type EvaluationBatchData = {
  id: string
  supplierId: string
  storeId: string
  status: string
  displayCode: string
  notes?: string | null
  createdAt: Date
  updatedAt: Date
}
