import type { UpdateEvaluationBatchDto } from '@app/application/EvaluationBatch'
import { UpdateEvaluationBatchSchema } from '@app/application/EvaluationBatch/EvaluationBatch.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateEvaluationBatchDto =
      UpdateEvaluationBatchSchema.parse(input)

    const model = new EvaluationBatch()

    return model.update(dto)
  }
}
