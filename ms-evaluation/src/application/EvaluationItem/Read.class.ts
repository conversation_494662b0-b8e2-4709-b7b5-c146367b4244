import {
  type FilterEvaluationItemDto,
  FilterEvaluationItemSchema,
} from '@app/application/EvaluationItem'

import { EvaluationItem } from '@app/domain/database/EvaluationItem'

export class Read {
  public async findById(id: string) {
    if (!id) {
      throw new ReferenceError('Evaluation Item ID is required')
    }

    const model = new EvaluationItem()

    return model.findById(id)
  }

  public async find(input: unknown) {
    const filter: FilterEvaluationItemDto =
      FilterEvaluationItemSchema.parse(input)

    const model = new EvaluationItem()

    return model.find(filter)
  }
}
