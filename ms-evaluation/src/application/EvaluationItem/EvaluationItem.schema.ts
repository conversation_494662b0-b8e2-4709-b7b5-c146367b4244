import { Decimal } from '@prisma/client/runtime/library'
import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination/PaginationSchema.schema'

export const EvaluationItemBaseSchema = z.object({
  evaluationBatchId: z.string().uuid(),
  supplierDescription: z.string().optional(),
  supplierSuggestedCategoryId: z.string().uuid().optional(),
  evaluatedProductCategoryId: z.string().uuid(),
  evaluatedProductClassificationId: z.string().uuid(),
  evaluatorNotes: z.string().optional(),
  proposedBuyPrice: z
    .number()
    .nonnegative()
    .transform((val) => new Decimal(val.toFixed(2))),
  status: z.enum(['PENDING_EVALUATION', 'ACCEPTED', 'REJECTED']),
  rejectionReason: z.string().optional(),
  estimatedMarketValue: z
    .number()
    .nonnegative()
    .optional()
    .transform((val) =>
      val !== undefined ? new Decimal(val.toFixed(2)) : undefined,
    ),
})

export const CreateEvaluationItemSchema = EvaluationItemBaseSchema

export const UpdateEvaluationItemSchema = EvaluationItemBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  evaluationBatchId: true,
  supplierDescription: true,
  supplierSuggestedCategoryId: true,
  evaluatedProductCategoryId: true,
  evaluatedProductClassificationId: true,
  evaluatorNotes: true,
  proposedBuyPrice: true,
  status: true,
  rejectionReason: true,
  estimatedMarketValue: true,
})

export const FilterEvaluationItemSchema = createFilterSchema({
  evaluationBatchId: z.string().uuid().optional(),
  status: z.enum(['PENDING_EVALUATION', 'ACCEPTED', 'REJECTED']).optional(),
  evaluatedProductCategoryId: z.string().uuid().optional(),
  evaluatedProductClassificationId: z.string().uuid().optional(),
})
