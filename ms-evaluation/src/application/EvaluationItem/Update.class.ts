import type { UpdateEvaluationItemDto } from '@app/application/EvaluationItem'
import { UpdateEvaluationItemSchema } from '@app/application/EvaluationItem/EvaluationItem.schema'

import { EvaluationItem } from '@app/domain/database/EvaluationItem'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateEvaluationItemDto = UpdateEvaluationItemSchema.parse(input)

    const model = new EvaluationItem()

    return model.update(dto)
  }
}
