import type { CreateEvaluationItemDto } from '@app/application/EvaluationItem'
import { CreateEvaluationItemSchema } from '@app/application/EvaluationItem/EvaluationItem.schema'

import { EvaluationBatch } from '@app/domain/database/EvaluationBatch'
import { EvaluationItem } from '@app/domain/database/EvaluationItem'

export class Create {
  public async create(input: unknown) {
    const dto: CreateEvaluationItemDto = CreateEvaluationItemSchema.parse(input)

    const evaluationBatchModel = new EvaluationBatch()

    await evaluationBatchModel.findById(dto.evaluationBatchId)

    const model = new EvaluationItem()

    return model.create(dto)
  }
}
