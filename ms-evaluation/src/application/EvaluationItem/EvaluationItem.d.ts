import type { z } from 'zod'

import type {
  CreateEvaluationItemSchema,
  FilterEvaluationItemSchema,
  UpdateEvaluationItemSchema,
} from '@app/application/EvaluationItem/EvaluationItem.schema'

export type CreateEvaluationItemDto = z.infer<typeof CreateEvaluationItemSchema>
export type UpdateEvaluationItemDto = z.infer<typeof UpdateEvaluationItemSchema>
export type FilterEvaluationItemDto = z.infer<typeof FilterEvaluationItemSchema>

export type FetchEvaluationItemsProps = FilterEvaluationItemDto

export type EvaluationItemData = {
  id: string
  evaluationBatchId: string
  supplierDescription?: string | null
  supplierSuggestedCategoryId?: string | null
  evaluatedProductCategoryId: string
  evaluatedProductClassificationId: string
  evaluatorNotes?: string | null
  proposedBuyPrice: number
  status: string
  rejectionReason?: string | null
  estimatedMarketValue?: number | null
  createdAt: Date
  updatedAt: Date
}
