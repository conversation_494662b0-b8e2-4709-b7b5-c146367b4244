import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import {
  Create,
  Delete as DeleteEvaluationItem,
  Read,
  Update,
} from '@app/application/EvaluationItem'
import { Language } from '@app/application/Language'

import type {
  EvaluationItemFilterDto,
  IdentifierDto,
  PostEvaluationItemDto,
  PutEvaluationItemDto,
} from '@app/resources/EvaluationItem/EvaluationItem'

export class EvaluationItemResource {
  @Get('/evaluation-items/:id')
  public async getEvaluationItemById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation item',
        Language.translate,
      )
    }
  }

  @Get('/evaluation-items')
  public async getEvaluationItems({ request, response }: ResourceMethodProps) {
    try {
      const {
        evaluationBatchId,
        status,
        evaluatedProductCategoryId,
        evaluatedProductClassificationId,
      } = request.query<EvaluationItemFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().find({
          page,
          perPage,
          search,
          sortBy,
          sortOrder,
          evaluationBatchId,
          status,
          evaluatedProductCategoryId,
          evaluatedProductClassificationId,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation item',
        Language.translate,
      )
    }
  }

  @Post('/evaluation-batches/:batchId/items')
  public async postEvaluationItem({ request, response }: ResourceMethodProps) {
    try {
      const { batchId } = request.params<{ batchId: string }>()
      const postEvaluationItemDto = request.body<PostEvaluationItemDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'evaluation item',
          },
        ),
        data: await new Create().create({
          ...postEvaluationItemDto,
          evaluationBatchId: batchId,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation item',
        Language.translate,
      )
    }
  }

  @Put('/evaluation-items/:id')
  public async putEvaluationItem({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putEvaluationItemDto = request.body<PutEvaluationItemDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Update().update({ ...putEvaluationItemDto, id }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation item',
        Language.translate,
      )
    }
  }

  @Delete('/evaluation-items/:id')
  public async deleteEvaluationItem({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteEvaluationItem().delete(id)

      response.send({
        code: ResourceMessageCode.C_204_0204,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0204}`,
          {
            title: 'evaluation item',
          },
        ),
        data: null,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation item',
        Language.translate,
      )
    }
  }
}
