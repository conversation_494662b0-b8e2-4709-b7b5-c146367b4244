export type IdentifierDto = {
  id: string
}

export type EvaluationItemFilterDto = {
  evaluationBatchId?: string
  status?: string
  evaluatedProductCategoryId?: string
  evaluatedProductClassificationId?: string
}

export type PostEvaluationItemDto = {
  evaluationBatchId: string
  supplierDescription?: string
  supplierSuggestedCategoryId?: string
  evaluatedProductCategoryId: string
  evaluatedProductClassificationId: string
  evaluatorNotes?: string
  proposedBuyPrice: number
  status: string
  rejectionReason?: string
  estimatedMarketValue?: number
}

export type PutEvaluationItemDto = {
  id: string
  supplierDescription?: string
  supplierSuggestedCategoryId?: string
  evaluatedProductCategoryId?: string
  evaluatedProductClassificationId?: string
  evaluatorNotes?: string
  proposedBuyPrice?: number
  status?: string
  rejectionReason?: string
  estimatedMarketValue?: number
}
