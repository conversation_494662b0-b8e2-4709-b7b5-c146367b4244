export type IdentifierDto = {
  id: string
}

export type EvaluationProposalFilterDto = {
  evaluationBatchId?: string
  status?: string
  fromDate?: string
  toDate?: string
}

export type PostEvaluationProposalDto = {
  evaluationBatchId: string
  totalProposedValue: number
  proposalDate?: string
  status: string
  supplierResponseDate?: string
  supplierNotes?: string
  supplierPaymentPreference?: string
}

export type PutEvaluationProposalDto = {
  id: string
  totalProposedValue?: number
  proposalDate?: string
  status?: string
  supplierResponseDate?: string
  supplierNotes?: string
  supplierPaymentPreference?: string
}

export type PutEvaluationProposalSendDto = {
  id: string
}

export type PutEvaluationProposalRespondDto = {
  id: string
  status: string
  supplierPaymentPreference?: string
  supplierNotes?: string
}
