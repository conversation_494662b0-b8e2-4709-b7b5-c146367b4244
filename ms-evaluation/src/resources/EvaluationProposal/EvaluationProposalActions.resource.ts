import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Put } from '@thrift/common/engines/Server'

import { RespondToProposal, Update } from '@app/application/EvaluationProposal'
import { Language } from '@app/application/Language'

import type {
  IdentifierDto,
  PutEvaluationProposalRespondDto,
  PutEvaluationProposalSendDto,
} from '@app/resources/EvaluationProposal/EvaluationProposal'

export class EvaluationProposalActionsResource {
  @Put('/evaluation-proposals/:id/send')
  public async putEvaluationProposalSend({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<PutEvaluationProposalSendDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Update().update({
          id,
          status: 'SENT_TO_SUPPLIER',
          proposalDate: new Date(),
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }

  @Put('/evaluation-proposals/:id/respond')
  public async putEvaluationProposalRespond({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const { status, supplierPaymentPreference, supplierNotes } =
        request.body<PutEvaluationProposalRespondDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new RespondToProposal().respond({
          id,
          status,
          supplierPaymentPreference,
          supplierNotes,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }
}
