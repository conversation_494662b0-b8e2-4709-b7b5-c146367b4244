import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Get, Post, Put } from '@thrift/common/engines/Server'

import { Create, Read, Update } from '@app/application/EvaluationProposal'
import { Language } from '@app/application/Language'

import type {
  EvaluationProposalFilterDto,
  IdentifierDto,
  PostEvaluationProposalDto,
  PutEvaluationProposalDto,
} from '@app/resources/EvaluationProposal/EvaluationProposal'

export class EvaluationProposalResource {
  @Get('/evaluation-proposals/:id')
  public async getEvaluationProposalById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }

  @Get('/evaluation-proposals')
  public async getEvaluationProposals({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { evaluationBatchId, status, fromDate, toDate } =
        request.query<EvaluationProposalFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      const filter = {
        page,
        perPage,
        search,
        sortBy,
        sortOrder,
        evaluationBatchId,
        status,
        ...(fromDate && { fromDate: new Date(fromDate) }),
        ...(toDate && { toDate: new Date(toDate) }),
      }

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().find(filter),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }

  @Post('/evaluation-proposals')
  public async postEvaluationProposal({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const postEvaluationProposalDto =
        request.body<PostEvaluationProposalDto>()

      const proposalData = {
        ...postEvaluationProposalDto,
        ...(postEvaluationProposalDto.proposalDate && {
          proposalDate: new Date(postEvaluationProposalDto.proposalDate),
        }),
        ...(postEvaluationProposalDto.supplierResponseDate && {
          supplierResponseDate: new Date(
            postEvaluationProposalDto.supplierResponseDate,
          ),
        }),
      }

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'evaluation proposal',
          },
        ),
        data: await new Create().create(proposalData),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }

  @Put('/evaluation-proposals/:id')
  public async putEvaluationProposal({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putEvaluationProposalDto = request.body<PutEvaluationProposalDto>()

      const proposalData = {
        ...putEvaluationProposalDto,
        id,
        ...(putEvaluationProposalDto.proposalDate && {
          proposalDate: new Date(putEvaluationProposalDto.proposalDate),
        }),
        ...(putEvaluationProposalDto.supplierResponseDate && {
          supplierResponseDate: new Date(
            putEvaluationProposalDto.supplierResponseDate,
          ),
        }),
      }

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Update().update(proposalData),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation proposal',
        Language.translate,
      )
    }
  }
}
