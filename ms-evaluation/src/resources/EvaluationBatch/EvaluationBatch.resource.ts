import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import {
  Create,
  Delete as DeleteEvaluationBatch,
  Read,
  Update,
} from '@app/application/EvaluationBatch'
import { Language } from '@app/application/Language'

import type {
  EvaluationBatchFilterDto,
  IdentifierDto,
  PostEvaluationBatchDto,
  PutEvaluationBatchDto,
} from '@app/resources/EvaluationBatch/EvaluationBatch'

export class EvaluationBatchResource {
  @Get('/evaluation-batches/:id')
  public async getEvaluationBatchById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation batch',
        Language.translate,
      )
    }
  }

  @Get('/evaluation-batches')
  public async getEvaluationBatches({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { supplierId, storeId, status, displayCode } =
        request.query<EvaluationBatchFilterDto>()

      const { page, perPage, search, sortBy, sortOrder } =
        request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().find({
          page,
          perPage,
          search,
          sortBy,
          sortOrder,
          supplierId,
          storeId,
          status,
          displayCode,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation batch',
        Language.translate,
      )
    }
  }

  @Post('/evaluation-batches')
  public async postEvaluationBatch({ request, response }: ResourceMethodProps) {
    try {
      const postEvaluationBatchDto = request.body<PostEvaluationBatchDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'evaluation batch',
          },
        ),
        data: await new Create().create(postEvaluationBatchDto),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation batch',
        Language.translate,
      )
    }
  }

  @Put('/evaluation-batches/:id')
  public async putEvaluationBatch({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const putEvaluationBatchDto = request.body<PutEvaluationBatchDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Update().update({ ...putEvaluationBatchDto, id }),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation batch',
        Language.translate,
      )
    }
  }

  @Delete('/evaluation-batches/:id')
  public async deleteEvaluationBatch({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteEvaluationBatch().delete(id)

      response.send({
        code: ResourceMessageCode.C_204_0204,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0204}`,
          {
            title: 'evaluation batch',
          },
        ),
        data: null,
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'evaluation batch',
        Language.translate,
      )
    }
  }
}
