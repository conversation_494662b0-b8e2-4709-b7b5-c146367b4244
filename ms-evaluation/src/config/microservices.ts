import config from 'app.config.json'

import { MicroserviceClient } from '@thrift/common/engines/Http'

// Create microservice clients
export const personServiceClient = new MicroserviceClient({
  baseURL: 'http://localhost:8081', // ms-person port
  serviceName: 'ms-person',
  timeout: 5000,
})

export const purchaseServiceClient = new MicroserviceClient({
  baseURL: 'http://localhost:8080', // ms-purchase port
  serviceName: 'ms-purchase',
  timeout: 5000,
})

// Export all microservice clients
export const microserviceClients = {
  person: personServiceClient,
  purchase: purchaseServiceClient,
}
