{"info": {"name": "Evaluation", "description": "API collection for the Evaluation microservice - manages evaluation process of used products submitted by suppliers", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8085", "type": "string"}], "item": [{"name": "Evaluation Batches", "item": [{"name": "/evaluation-batches", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplierId\": \"supplier-001-uuid-sample\",\n  \"storeId\": \"store-001-uuid-sample\",\n  \"status\": \"PENDING_REVIEW\",\n  \"displayCode\": \"EB004\",\n  \"notes\": \"New evaluation batch for testing\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches", "host": ["{{baseUrl}}"], "path": ["evaluation-batches"]}}}, {"name": "/evaluation-batches", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-batches", "host": ["{{baseUrl}}"], "path": ["evaluation-batches"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "supplierId", "value": "", "disabled": true}, {"key": "storeId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "search", "value": "", "disabled": true}]}}}, {"name": "/evaluation-batches/:id", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}}, {"name": "/evaluation-batches/:id", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"UNDER_EVALUATION\",\n  \"notes\": \"Updated notes for evaluation batch\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}}, {"name": "/evaluation-batches/:id", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}}]}, {"name": "Evaluation Items", "item": [{"name": "/evaluation-batches/:batchId/items", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplierDescription\": \"Vintage denim jacket\",\n  \"supplierSuggestedCategoryId\": \"cat-clothing-001\",\n  \"evaluatedProductCategoryId\": \"cat-clothing-001\",\n  \"evaluatedProductClassificationId\": \"class-vintage-001\",\n  \"evaluatorNotes\": \"Good condition, authentic vintage piece\",\n  \"proposedBuyPrice\": 55.00,\n  \"status\": \"PENDING_EVALUATION\",\n  \"estimatedMarketValue\": 150.00\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches/:batchId/items", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":batchId", "items"], "variable": [{"key": "batchId", "value": "eb-001-uuid-sample-batch-001"}]}}}, {"name": "/evaluation-items", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-items?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["evaluation-items"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "evaluationBatchId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}}}, {"name": "/evaluation-items/:id", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"key": "id", "value": "ei-001-uuid-sample-item-001"}]}}}, {"name": "/evaluation-items/:id", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACCEPTED\",\n  \"evaluatorNotes\": \"Excellent condition, high resale value\",\n  \"proposedBuyPrice\": 60.00\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"key": "id", "value": "ei-001-uuid-sample-item-001"}]}}}, {"name": "/evaluation-items/:id", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"key": "id", "value": "ei-001-uuid-sample-item-001"}]}}}]}, {"name": "Evaluation Proposals", "item": [{"name": "/evaluation-proposals", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"evaluationBatchId\": \"eb-001-uuid-sample-batch-001\",\n  \"totalProposedValue\": 130.00,\n  \"status\": \"DRAFT\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-proposals", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals"]}}}, {"name": "/evaluation-proposals", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-proposals?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "evaluationBatchId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}}}, {"name": "/evaluation-proposals/:id", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id"], "variable": [{"key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}}, {"name": "/evaluation-proposals/:id/send", "request": {"method": "PUT", "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id/send", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id", "send"], "variable": [{"key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}}, {"name": "/evaluation-proposals/:id/respond", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACCEPTED_BY_SUPPLIER\",\n  \"supplierNotes\": \"Agreed to all proposed values. Ready for pickup.\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id/respond", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id", "respond"], "variable": [{"key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}}]}]}