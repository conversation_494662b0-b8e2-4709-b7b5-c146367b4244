model ProductCategoryMeasurements {
  id                String          @id @default(uuid())
  productCategory   ProductCategory @relation(fields: [productCategoryId], references: [id])
  productCategoryId String
  size              String
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  deleted           Boolean         @default(false)

  @@map("product_category_measurements")
}
