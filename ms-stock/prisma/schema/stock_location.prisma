model StockLocation {
  id                    String          @id @default(uuid())
  parentStockLocationId String?
  parentStockLocation   StockLocation?  @relation("ParentChildStockLocation", fields: [parentStockLocationId], references: [id])
  children              StockLocation[] @relation("ParentChildStockLocation")
  description           String
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  deleted               Boolean         @default(false)
  stocks                Stock[]

  @@map("stock_locations")
}
