model Stock {
  id                      String        @id @default(uuid())
  productId               String
  productClassificationId String
  stockLocationId         String
  stockLocation           StockLocation @relation(fields: [stockLocationId], references: [id])
  notes                   String?
  buyingPrice             Float
  expectedSellingPrice    Float
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt
  deleted                 Boolean       @default(false)

  @@map("stock")
}
