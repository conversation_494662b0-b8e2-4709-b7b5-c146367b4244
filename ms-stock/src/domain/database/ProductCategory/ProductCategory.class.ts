import { PrismaClient } from '@prisma/client'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductCategoryDto,
  FetchProductCategoriesProps,
  UpdateProductCategoryDto,
} from '@app/domain/database/ProductCategory'
import i18next from '@app/domain/locales/i18n/i18next'

export class ProductCategory {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const productCategory = await this.prisma.productCategory.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        name: true,
        description: true,
      },
    })

    if (!productCategory) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product.category' }),
      )
    }

    return productCategory
  }

  public async findByName(name: string) {
    return this.prisma.productCategory.findFirst({
      where: { name, deleted: false },
      select: {
        id: true,
        name: true,
        description: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchProductCategoriesProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
        ],
      }),
    }

    const [productCategories, totalItems] = await this.prisma.$transaction([
      this.prisma.productCategory.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          name: true,
          description: true,
        },
      }),
      this.prisma.productCategory.count({ where }),
    ])

    return {
      items: productCategories,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({ name, description }: CreateProductCategoryDto) {
    const productCategory = await this.prisma.productCategory.create({
      data: {
        name,
        description,
      },
      select: {
        id: true,
        name: true,
        description: true,
      },
    })

    return productCategory
  }

  public async update({ id, name, description }: UpdateProductCategoryDto) {
    const productCategory = await this.prisma.productCategory.update({
      where: { id },
      data: {
        name,
        description,
      },
    })

    return productCategory
  }

  public async delete(id: string) {
    const productCategory = await this.prisma.productCategory.update({
      where: { id },
      data: { deleted: true },
    })

    return productCategory
  }
}
