import { PrismaClient } from '@prisma/client'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductCategoryMeasurementsDto,
  FetchProductCategoryMeasurementsProps,
  UpdateProductCategoryMeasurementsDto,
} from '@app/domain/database/ProductCategoryMeasurements'
import i18next from '@app/domain/locales/i18n/i18next'

export class ProductCategoryMeasurements {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const productCategoryMeasurements =
      await this.prisma.productCategoryMeasurements.findFirst({
        where: { id, deleted: false },
        include: {
          productCategory: true,
        },
      })

    if (!productCategoryMeasurements) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product category measurements' }),
      )
    }

    return productCategoryMeasurements
  }

  public async findByProductCategoryIdAndSize(
    productCategoryId: string,
    size: string,
  ) {
    return this.prisma.productCategoryMeasurements.findFirst({
      where: { productCategoryId, size, deleted: false },
    })
  }

  public async findByProductCategoryId(productCategoryId: string) {
    return this.prisma.productCategoryMeasurements.findMany({
      where: { productCategoryId, deleted: false },
      include: {
        productCategory: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchProductCategoryMeasurementsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ size: { contains: search } }],
      }),
    }

    const [productCategoryMeasurements, totalItems] =
      await this.prisma.$transaction([
        this.prisma.productCategoryMeasurements.findMany({
          where,
          skip,
          take: +perPage,
          orderBy: { [sortBy]: sortOrder },
          include: {
            productCategory: true,
          },
        }),
        this.prisma.productCategoryMeasurements.count({ where }),
      ])

    return {
      items: productCategoryMeasurements,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    productCategoryId,
    size,
  }: CreateProductCategoryMeasurementsDto) {
    const productCategoryMeasurements =
      await this.prisma.productCategoryMeasurements.create({
        data: {
          productCategoryId,
          size,
        },
        include: {
          productCategory: true,
        },
      })

    return productCategoryMeasurements
  }

  public async update({
    id,
    productCategoryId,
    size,
  }: UpdateProductCategoryMeasurementsDto) {
    const productCategoryMeasurements =
      await this.prisma.productCategoryMeasurements.update({
        where: { id },
        data: {
          productCategoryId,
          size,
        },
        include: {
          productCategory: true,
        },
      })

    return productCategoryMeasurements
  }

  public async delete(id: string) {
    const productCategoryMeasurements =
      await this.prisma.productCategoryMeasurements.update({
        where: { id },
        data: { deleted: true },
      })

    return productCategoryMeasurements
  }
}
