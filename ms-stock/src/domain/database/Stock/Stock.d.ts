import type { PaginationParams } from '@thrift/common/engines/Pagination/Pagination'

export type CreateStockDto = {
  productId: string
  productClassificationId: string
  stockLocationId?: string
  notes?: string
  buyingPrice: number
  expectedSellingPrice: number
}

export type UpdateStockDto = Partial<
  Omit<CreateStockDto, 'productId' | 'productClassificationId'>
> & {
  id: string
  productId?: string
  productClassificationId?: string
}

export type StockData = {
  id: string
  productId: string
  productClassificationId: string
  stockLocationId?: string
  notes?: string
  buyingPrice: number
  expectedSellingPrice: number
}

export type FetchStocksProps = PaginationParams & {
  productId?: string
  productClassificationId?: string
  stockLocationId?: string
}
