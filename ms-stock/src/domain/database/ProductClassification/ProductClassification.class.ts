import { PrismaClient } from '@prisma/client'
import i18next from 'i18next'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductClassificationDto,
  FetchProductClassificationsProps,
  UpdateProductClassificationDto,
} from '@app/domain/database/ProductClassification'

export class ProductClassification {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const productClassification =
      await this.prisma.productClassification.findFirst({
        where: { id, deleted: false },
        select: {
          id: true,
          name: true,
          description: true,
          coefficient: true,
        },
      })

    if (!productClassification) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product.classification' }),
      )
    }

    return productClassification
  }

  public async findByName(name: string) {
    return this.prisma.productClassification.findFirst({
      where: { name, deleted: false },
      select: {
        id: true,
        name: true,
        description: true,
        coefficient: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchProductClassificationsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
        ],
      }),
    }

    const [productClassifications, totalItems] = await this.prisma.$transaction(
      [
        this.prisma.productClassification.findMany({
          where,
          skip,
          take: +perPage,
          orderBy: { [sortBy]: sortOrder },
          select: {
            id: true,
            name: true,
            description: true,
            coefficient: true,
          },
        }),
        this.prisma.productClassification.count({ where }),
      ],
    )

    return {
      items: productClassifications,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    name,
    description,
    coefficient,
  }: CreateProductClassificationDto) {
    const productClassification =
      await this.prisma.productClassification.create({
        data: {
          name,
          description,
          coefficient,
        },
        select: {
          id: true,
          name: true,
          description: true,
          coefficient: true,
        },
      })

    return productClassification
  }

  public async update({
    id,
    name,
    description,
    coefficient,
  }: UpdateProductClassificationDto) {
    const productClassification =
      await this.prisma.productClassification.update({
        where: { id },
        data: {
          name,
          description,
          coefficient,
        },
        select: {
          id: true,
          name: true,
          description: true,
          coefficient: true,
        },
      })

    return productClassification
  }

  public async delete(id: string) {
    return this.prisma.productClassification.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
