import { PrismaClient } from '@prisma/client'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductDto,
  FetchProductsProps,
  UpdateProductDto,
} from '@app/domain/database/Product'
import i18next from '@app/domain/locales/i18n/i18next'

export class Product {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const product = await this.prisma.product.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        name: true,
      },
    })

    if (!product) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product' }),
      )
    }

    return product
  }

  public async findByName(name: string) {
    return this.prisma.product.findFirst({
      where: { name, deleted: false },
      select: {
        id: true,
        name: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchProductsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ name: { contains: search } }],
      }),
    }

    const [product, totalItems] = await this.prisma.$transaction([
      this.prisma.product.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          name: true,
        },
      }),
      this.prisma.product.count({ where }),
    ])

    return {
      items: product,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({ name, productCategoryId }: CreateProductDto) {
    const product = await this.prisma.product.create({
      data: {
        name,
        productCategory: {
          connect: { id: productCategoryId },
        },
      },
      select: {
        id: true,
        name: true,
      },
    })

    return product
  }

  public async update({ id, name, productCategoryId }: UpdateProductDto) {
    const product = await this.prisma.product.update({
      where: { id },
      data: {
        name,
        productCategory: {
          connect: { id: productCategoryId },
        },
      },
    })

    return product
  }

  public async delete(id: string) {
    const product = await this.prisma.product.update({
      where: { id },
      data: { deleted: true },
    })

    return product
  }
}
