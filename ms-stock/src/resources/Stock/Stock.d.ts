export type IdentifierDto = {
  id: string
}

export type StockIdentifierDto = {
  StockId: string
}

export type PostStockDto = {
  productId: string
  productClassificationId: string
  stockLocationId: string
  notes?: string
  buyingPrice: number
  expectedSellingPrice: number
}

export type PutStockDto = Partial<PostStockDto>

export type StockFilterDto = {
  productId?: string
  productClassificationId?: string
  stockLocationId?: string
}
