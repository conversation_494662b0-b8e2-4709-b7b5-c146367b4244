import { Debug } from '@thrift/common/engines/Debug'
import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteStock,
  Read,
  Update,
} from '@app/application/Stock'

import type {
  IdentifierDto,
  PostStockDto,
  PutStockDto,
  StockFilterDto,
} from '@app/resources/Stock/Stock'

export class StockResource {
  @Get('/stocks/:id')
  public async getStockById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'stock',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/stocks')
  public async getStocks({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()
      const filterParams = request.query<StockFilterDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'stock',
          },
        ),
        data: await new Read().find({
          ...paginationParams,
          ...filterParams,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'stock',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/stocks')
  public async postStock({ request, response }: ResourceMethodProps) {
    try {
      const postStockDto = request.body<PostStockDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'stock',
          },
        ),
        data: await new Create().create(postStockDto),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/stocks/:id')
  public async updateStock({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateStockDto = request.body<PutStockDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'stock',
          },
        ),
        data: await new Update().update({
          id,
          ...updateStockDto,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/stocks/:id')
  public async deleteStock({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteStock().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'stock',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
