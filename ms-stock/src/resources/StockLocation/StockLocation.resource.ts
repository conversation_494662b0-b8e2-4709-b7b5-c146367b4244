import { Debug } from '@thrift/common/engines/Debug'
import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteStockLocation,
  Read,
  Update,
} from '@app/application/StockLocation'

import type {
  IdentifierDto,
  PostStockLocationDto,
} from '@app/resources/StockLocation/StockLocation'

export class StockLocationResource {
  @Get('/stock-locations/:id')
  public async getStockLocationById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'stock location',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/stock-locations')
  public async getStockLocations({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'stock location',
          },
        ),
        data: await new Read().find(paginationParams),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'stock location',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/stock-locations')
  public async postStockLocation({ request, response }: ResourceMethodProps) {
    try {
      const postStockLocationDto = request.body<PostStockLocationDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'stock location',
          },
        ),
        data: await new Create().create(postStockLocationDto),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock location',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/stock-locations/:id')
  public async updateStockLocation({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateStockLocationDto = request.body<PostStockLocationDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'stock location',
          },
        ),
        data: await new Update().update({
          id,
          ...updateStockLocationDto,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock location',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/stock-locations/:id')
  public async deleteStockLocation({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteStockLocation().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'stock location',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'stock location',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
