// src/resources/ProductCategory/ProductCategoryNested.resource.ts
import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create as CreateProductCategoryMeasurements,
  Delete as DeleteProductCategoryMeasurements,
  Read as ReadProductCategoryMeasurements,
} from '@app/application/ProductCategoryMeasurements'

import type {
  DeleteProductCategoryMeasurementDto,
  PostProductCategoryMeasurementDto,
  ProductCategoryIdentifierDto,
} from '@app/resources/ProductCategory/ProductCategory'

export class ProductCategoryNestedResource {
  @Get('/product-categories/:productCategoryId/measurements')
  public async getProductCategoryMeasurementsByProductCategoryId({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productCategoryId } =
        request.params<ProductCategoryIdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new ReadProductCategoryMeasurements().findByProductCategoryId(
          productCategoryId,
        ),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'product category measurements',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/product-categories/:productCategoryId/measurements')
  public async createProductCategoryMeasurement({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productCategoryId } =
        request.params<ProductCategoryIdentifierDto>()

      const { size } = request.body<PostProductCategoryMeasurementDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'product category measurement',
          },
        ),
        data: await new CreateProductCategoryMeasurements().create({
          productCategoryId,
          size,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product category measurement',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/product-categories/:productCategoryId/measurements')
  public async deleteProductCategoryMeasurement({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productCategoryId } =
        request.params<ProductCategoryIdentifierDto>()

      const { size } = request.body<DeleteProductCategoryMeasurementDto>()

      await new DeleteProductCategoryMeasurements().deleteByProductCategoryIdAndSize(
        productCategoryId,
        size,
      )

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'product category measurement',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product category measurement',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
