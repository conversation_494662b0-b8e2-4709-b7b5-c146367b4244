import { Debug } from '@thrift/common/engines/Debug'
import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteProduct,
  Read,
  Update,
} from '@app/application/Product'

import type {
  IdentifierDto,
  PostProductDto,
} from '@app/resources/Product/Product'

export class ProductResource {
  @Get('/product/:id')
  public async getProductById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new <PERSON>().findById(id),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'product',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/product')
  public async getProducts({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'product',
          },
        ),
        data: await new Read().find(paginationParams),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'product',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/product')
  public async postProduct({ request, response }: ResourceMethodProps) {
    try {
      const postProductDto = request.body<PostProductDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'product',
          },
        ),
        data: await new Create().create(postProductDto),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/product/:id')
  public async updateProduct({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateProductDto = request.body<PostProductDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'product',
          },
        ),
        data: await new Update().update({
          id,
          ...updateProductDto,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/product/:id')
  public async deleteProduct({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteProduct().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'product',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
