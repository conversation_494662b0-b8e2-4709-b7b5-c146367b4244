import { Debug } from '@thrift/common/engines/Debug'
import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteProductCategoryMeasurements,
  Read,
  Update,
} from '@app/application/ProductCategoryMeasurements'

import type {
  IdentifierDto,
  PostProductCategoryMeasurementsDto,
} from '@app/resources/ProductCategoryMeasurements/ProductCategoryMeasurements'

export class ProductCategoryMeasurementsResource {
  @Get('/product-category-measurements/:id')
  public async getProductCategoryMeasurementsById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'product category measurements',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/product-category-measurements')
  public async getProductCategoryMeasurements({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'product category measurements',
          },
        ),
        data: await new Read().find(paginationParams),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', {
            name: 'product category measurements',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/product-category-measurements')
  public async postProductCategoryMeasurements({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const postProductCategoryMeasurementsDto =
        request.body<PostProductCategoryMeasurementsDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'product category measurements',
          },
        ),
        data: await new Create().create(postProductCategoryMeasurementsDto),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product category measurements',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/product-category-measurements/:id')
  public async updateProductCategoryMeasurements({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      const updateProductCategoryMeasurementsDto =
        request.body<PostProductCategoryMeasurementsDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'product category measurements',
          },
        ),
        data: await new Update().update({
          id,
          ...updateProductCategoryMeasurementsDto,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'product category measurements',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/product-category-measurements/:id')
  public async deleteProductCategoryMeasurements({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteProductCategoryMeasurements().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'product category measurements',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      response.send({
        code: ResourceMessageCode.C_500_0500,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_500_0500}`,
        ),
      })
    }
  }
}
