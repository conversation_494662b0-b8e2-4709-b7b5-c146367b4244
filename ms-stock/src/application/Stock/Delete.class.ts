import { Language } from '@app/application/Language'

import { Stock } from '@app/domain/database/Stock'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock.id' }),
      )
    }

    const model = new Stock()

    // Check if the stock exists
    const stock = await model.findById(id)

    if (!stock) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'stock' }),
      )
    }

    // Delete the stock
    return model.delete(id)
  }
}
