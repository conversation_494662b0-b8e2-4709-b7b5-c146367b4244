import { Language } from '@app/application/Language'
import type { CreateStockDto } from '@app/application/Stock/Stock'

import { Product } from '@app/domain/database/Product'
import { ProductClassification } from '@app/domain/database/ProductClassification'
import { Stock } from '@app/domain/database/Stock'
import { StockLocation } from '@app/domain/database/StockLocation'

export class Create {
  public async create({
    productId,
    productClassificationId,
    stockLocationId,
    notes,
    buyingPrice,
    expectedSellingPrice,
  }: CreateStockDto) {
    if (
      !productId ||
      !productClassificationId ||
      !stockLocationId ||
      !buyingPrice ||
      !expectedSellingPrice
    ) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock' }),
      )
    }

    // Validate that buying price is a positive number
    if (Number(buyingPrice) <= 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.buyingPrice',
        }),
      )
    }

    // Validate that expected selling price is a positive number
    if (Number(expectedSellingPrice) <= 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.expectedSellingPrice',
        }),
      )
    }

    // Validate that the product exists
    const productModel = new Product()

    const product = await productModel.findById(productId)

    if (!product) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'product' }),
      )
    }

    // Validate that the product classification exists
    const productClassificationModel = new ProductClassification()

    await productClassificationModel.findById(productClassificationId)

    // Validate that the stock location exists
    const stockLocationModel = new StockLocation()

    await stockLocationModel.findById(stockLocationId)

    // Create the stock
    const stockModel = new Stock()

    return stockModel.create({
      productId,
      productClassificationId,
      stockLocationId,
      notes,
      buyingPrice,
      expectedSellingPrice,
    })
  }
}
