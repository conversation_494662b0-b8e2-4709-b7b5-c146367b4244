import { Language } from '@app/application/Language'
import type { UpdateStockDto } from '@app/application/Stock/Stock'

import { Product } from '@app/domain/database/Product'
import { ProductClassification } from '@app/domain/database/ProductClassification'
import { Stock } from '@app/domain/database/Stock'
import { StockLocation } from '@app/domain/database/StockLocation'

export class Update {
  public async update({
    id,
    productId,
    productClassificationId,
    stockLocationId,
    notes,
    buyingPrice,
    expectedSellingPrice,
  }: UpdateStockDto) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock.id' }),
      )
    }

    const stockModel = new Stock()

    // Check if the stock exists
    await stockModel.findById(id)

    // Validate that the product exists if provided
    if (productId) {
      const productModel = new Product()

      await productModel.findById(productId)
    }

    // Validate that the product classification exists if provided
    if (productClassificationId) {
      const productClassificationModel = new ProductClassification()

      await productClassificationModel.findById(productClassificationId)
    }

    // Validate that the stock location exists if provided
    if (stockLocationId) {
      const stockLocationModel = new StockLocation()

      await stockLocationModel.findById(stockLocationId)
    }

    // Validate that buying price is a positive number if provided
    if (buyingPrice !== undefined && Number(buyingPrice) <= 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.buyingPrice',
        }),
      )
    }

    // Validate that expected selling price is a positive number if provided
    if (
      expectedSellingPrice !== undefined &&
      Number(expectedSellingPrice) <= 0
    ) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.expectedSellingPrice',
        }),
      )
    }

    // Update the stock
    return stockModel.update({
      id,
      productId,
      productClassificationId,
      stockLocationId,
      notes,
      buyingPrice,
      expectedSellingPrice,
    })
  }
}
