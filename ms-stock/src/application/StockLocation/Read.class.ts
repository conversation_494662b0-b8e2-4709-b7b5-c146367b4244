import type { FetchStockLocationsProps } from '@app/application/StockLocation/StockLocation'

import { StockLocation } from '@app/domain/database/StockLocation'

export class Read {
  public async findById(id: string) {
    const model = new StockLocation()

    return model.findById(id)
  }

  public async findByDescription(description: string) {
    const model = new StockLocation()

    return model.findByDescription(description)
  }

  public async find(params: FetchStockLocationsProps) {
    const model = new StockLocation()

    return model.find(params)
  }
}
