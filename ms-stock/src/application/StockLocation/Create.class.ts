import { Language } from '@app/application/Language'
import type { CreateStockLocationDto } from '@app/application/StockLocation/StockLocation'

import { StockLocation } from '@app/domain/database/StockLocation'

export class Create {
  public async create({
    description,
    parentStockLocationId,
  }: CreateStockLocationDto) {
    if (!description) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock.location' }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.location.description',
        }),
      )
    }

    const model = new StockLocation()

    // Check if the description already exists
    const existingStockLocation = await model.findByDescription(description)

    if (existingStockLocation) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.location.description',
        }),
      )
    }

    // If parentStockLocationId is provided, check if it exists
    if (parentStockLocationId) {
      const parentStockLocation = await model.findById(parentStockLocationId)

      if (!parentStockLocation) {
        throw new ReferenceError(
          Language.translate('common:notFound', {
            name: 'stock.location.parentStockLocationId',
          }),
        )
      }
    }

    // Create the stock location
    return model.create({ description, parentStockLocationId })
  }
}
