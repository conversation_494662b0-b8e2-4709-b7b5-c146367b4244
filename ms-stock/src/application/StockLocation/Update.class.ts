import { Language } from '@app/application/Language'
import type { UpdateStockLocationDto } from '@app/application/StockLocation/StockLocation'

import { StockLocation } from '@app/domain/database/StockLocation'

export class Update {
  public async update({
    id,
    description,
    parentStockLocationId,
  }: UpdateStockLocationDto) {
    if (!id || !description) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock.location' }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.location.description',
        }),
      )
    }

    const model = new StockLocation()

    // Check if the stock location exists
    const stockLocation = await model.findById(id)

    if (!stockLocation) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'stock.location' }),
      )
    }

    // Check if the description already exists for another stock location
    const existingStockLocation = await model.findByDescription(description)

    if (existingStockLocation && existingStockLocation.id !== id) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'stock.location.description',
        }),
      )
    }

    // If parentStockLocationId is provided, check if it exists and is not the same as id
    if (parentStockLocationId) {
      if (parentStockLocationId === id) {
        throw new ReferenceError(
          Language.translate('common:invalid', {
            name: 'stock.location.parentStockLocationId',
          }),
        )
      }
      const parentStockLocation = await model.findById(parentStockLocationId)

      if (!parentStockLocation) {
        throw new ReferenceError(
          Language.translate('common:notFound', {
            name: 'stock.location.parentStockLocationId',
          }),
        )
      }
    }

    // Update the stock location
    return model.update({ id, description, parentStockLocationId })
  }
}
