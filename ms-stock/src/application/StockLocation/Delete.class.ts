import { Language } from '@app/application/Language'

import { StockLocation } from '@app/domain/database/StockLocation'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'stock.location.id' }),
      )
    }

    const model = new StockLocation()

    // Check if the stock location exists
    const stockLocation = await model.findById(id)

    if (!stockLocation) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'stock.location' }),
      )
    }

    // Delete the stock location
    return model.delete(id)
  }
}
