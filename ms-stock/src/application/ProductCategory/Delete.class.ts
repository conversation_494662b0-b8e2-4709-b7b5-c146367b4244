import { Language } from '@app/application/Language'

import { ProductCategory } from '@app/domain/database/ProductCategory'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product.category.id' }),
      )
    }

    const model = new ProductCategory()

    // Check if the product category exists
    await model.findById(id)

    // Delete the product category
    return model.delete(id)
  }
}
