import { Language } from '@app/application/Language'
import type { CreateProductCategoryDto } from '@app/application/ProductCategory/ProductCategory'

import { ProductCategory } from '@app/domain/database/ProductCategory'

export class Create {
  public async create({ name, description }: CreateProductCategoryDto) {
    if (!name || !description) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product.category' }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.name',
        }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.description',
        }),
      )
    }

    const model = new ProductCategory()

    // Check if the name already exists
    const existingCategory = await model.findByName(name)

    if (existingCategory) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.name',
        }),
      )
    }

    // Create the product category
    return model.create({ name, description })
  }
}
