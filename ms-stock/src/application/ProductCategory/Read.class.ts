import type { FetchProductCategoriesProps } from '@app/application/ProductCategory/ProductCategory'

import { ProductCategory } from '@app/domain/database/ProductCategory'

export class Read {
  public async findById(id: string) {
    const model = new ProductCategory()

    return model.findById(id)
  }

  public async findByName(name: string) {
    const model = new ProductCategory()

    return model.findByName(name)
  }

  public async find(params: FetchProductCategoriesProps) {
    const model = new ProductCategory()

    return model.find(params)
  }
}
