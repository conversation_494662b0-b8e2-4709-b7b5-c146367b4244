import { Language } from '@app/application/Language'
import type { UpdateProductCategoryDto } from '@app/application/ProductCategory/ProductCategory'

import { ProductCategory } from '@app/domain/database/ProductCategory'

export class Update {
  public async update({ id, name, description }: UpdateProductCategoryDto) {
    if (!id || !name || !description) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product.category' }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.name',
        }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.description',
        }),
      )
    }

    const model = new ProductCategory()

    // Check if the product category exists
    await model.findById(id)

    // Check if the name already exists for another product category
    const existingCategory = await model.findByName(name)

    if (existingCategory && existingCategory.id !== id) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.category.name',
        }),
      )
    }

    // Update the product category
    return model.update({ id, name, description })
  }
}
