import { Language } from '@app/application/Language'
import type { CreateProductDto } from '@app/application/Product/Product'

import { Product } from '@app/domain/database/Product'

export class Create {
  public async create({ name, productCategoryId }: CreateProductDto) {
    if (!name || !productCategoryId) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product' }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.name',
        }),
      )
    }

    const model = new Product()

    // Check if the name already exists
    const existingProduct = await model.findByName(name)

    if (existingProduct) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.name',
        }),
      )
    }

    // Create the product
    return model.create({ name, productCategoryId })
  }
}
