import { Language } from '@app/application/Language'

import { Product } from '@app/domain/database/Product'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product.id' }),
      )
    }

    const model = new Product()

    // Check if the product exists
    await model.findById(id)

    // Delete the product
    return model.delete(id)
  }
}
