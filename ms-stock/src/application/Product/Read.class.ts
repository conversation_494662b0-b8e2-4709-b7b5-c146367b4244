import type { FetchProductsProps } from '@app/application/Product/Product'

import { Product } from '@app/domain/database/Product'

export class Read {
  public async findById(id: string) {
    const model = new Product()

    return model.findById(id)
  }

  public async findByName(name: string) {
    const model = new Product()

    return model.findByName(name)
  }

  public async find(params: FetchProductsProps) {
    const model = new Product()

    return model.find(params)
  }
}
