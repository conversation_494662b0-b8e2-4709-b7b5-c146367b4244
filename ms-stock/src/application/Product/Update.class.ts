import { Language } from '@app/application/Language'
import type { UpdateProductDto } from '@app/application/Product/Product'

import { Product } from '@app/domain/database/Product'

export class Update {
  public async update({ id, name, productCategoryId }: UpdateProductDto) {
    if (!id || !name || !productCategoryId) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'product' }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.name',
        }),
      )
    }

    const model = new Product()

    // Check if the product exists
    await model.findById(id)

    // Check if the name already exists for another product
    const existingProduct = await model.findByName(name)

    if (existingProduct && existingProduct.id !== id) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.name',
        }),
      )
    }

    // Update the product
    return model.update({ id, name, productCategoryId })
  }
}
