import { Language } from '@app/application/Language'

import { ProductCategory } from '@app/domain/database/ProductCategory'
import { ProductCategoryMeasurements } from '@app/domain/database/ProductCategoryMeasurements'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product category measurements.id',
        }),
      )
    }

    const model = new ProductCategoryMeasurements()

    // Check if the product category measurements exists
    await model.findById(id)

    // Delete the product category measurements
    return model.delete(id)
  }

  public async deleteByProductCategoryIdAndSize(
    productCategoryId: string,
    size: string,
  ) {
    if (!productCategoryId || !size) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product category measurements',
        }),
      )
    }

    // Check if the product category exists
    const productCategoryModel = new ProductCategory()

    await productCategoryModel.findById(productCategoryId)

    const model = new ProductCategoryMeasurements()

    // Check if the measurement exists for this product category
    const existingMeasurement = await model.findByProductCategoryIdAndSize(
      productCategoryId,
      size,
    )

    if (!existingMeasurement) {
      throw new ReferenceError(
        Language.translate('common:notFound', {
          name: 'product category measurements',
        }),
      )
    }

    // Delete the product category measurements
    return model.delete(existingMeasurement.id)
  }
}
