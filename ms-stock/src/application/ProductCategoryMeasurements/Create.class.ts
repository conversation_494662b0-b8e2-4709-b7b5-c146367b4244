import { Language } from '@app/application/Language'
import type { CreateProductCategoryMeasurementsDto } from '@app/application/ProductCategoryMeasurements/ProductCategoryMeasurements'

import { ProductCategory } from '@app/domain/database/ProductCategory'
import { ProductCategoryMeasurements } from '@app/domain/database/ProductCategoryMeasurements'

export class Create {
  public async create({
    productCategoryId,
    size,
  }: CreateProductCategoryMeasurementsDto) {
    if (!productCategoryId || !size) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product category measurements',
        }),
      )
    }

    if (size.length < 1) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product category measurements.size',
        }),
      )
    }

    // Check if the product category exists
    const productCategoryModel = new ProductCategory()

    await productCategoryModel.findById(productCategoryId)

    const model = new ProductCategoryMeasurements()

    // Check if the measurement already exists for this product category
    const existingMeasurement = await model.findByProductCategoryIdAndSize(
      productCategoryId,
      size,
    )

    if (existingMeasurement) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product category measurements.size',
        }),
      )
    }

    // Create the product category measurements
    return model.create({ productCategoryId, size })
  }
}
