import type { FetchProductCategoryMeasurementsProps } from '@app/application/ProductCategoryMeasurements/ProductCategoryMeasurements'

import { ProductCategoryMeasurements } from '@app/domain/database/ProductCategoryMeasurements'

export class Read {
  public async findById(id: string) {
    const model = new ProductCategoryMeasurements()

    return model.findById(id)
  }

  public async findByProductCategoryId(productCategoryId: string) {
    const model = new ProductCategoryMeasurements()

    return model.findByProductCategoryId(productCategoryId)
  }

  public async find(params: FetchProductCategoryMeasurementsProps) {
    const model = new ProductCategoryMeasurements()

    return model.find(params)
  }
}
