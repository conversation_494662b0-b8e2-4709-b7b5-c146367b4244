import type { FetchProductClassificationsProps } from '@app/application/ProductClassification/ProductClassification'

import { ProductClassification } from '@app/domain/database/ProductClassification'

export class Read {
  public async findById(id: string) {
    const model = new ProductClassification()

    return model.findById(id)
  }

  public async findByName(name: string) {
    const model = new ProductClassification()

    return model.findByName(name)
  }

  public async find(params: FetchProductClassificationsProps) {
    const model = new ProductClassification()

    return model.find(params)
  }
}
