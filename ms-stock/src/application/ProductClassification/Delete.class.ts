import { Language } from '@app/application/Language'

import { ProductClassification } from '@app/domain/database/ProductClassification'

export class Delete {
  public async delete(id: string) {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.id',
        }),
      )
    }

    const model = new ProductClassification()

    // Delete the product classification
    return model.delete(id)
  }
}
