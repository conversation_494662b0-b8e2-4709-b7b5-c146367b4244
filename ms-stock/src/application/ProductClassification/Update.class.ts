import { Language } from '@app/application/Language'
import type { UpdateProductClassificationDto } from '@app/application/ProductClassification/ProductClassification'

import { ProductClassification } from '@app/domain/database/ProductClassification'

export class Update {
  public async update({
    id,
    name,
    description,
    coefficient,
  }: UpdateProductClassificationDto) {
    if (!id || !name || !description || coefficient === undefined) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification',
        }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.name',
        }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.description',
        }),
      )
    }

    if (coefficient <= 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.coefficient',
        }),
      )
    }

    const model = new ProductClassification()

    // Check if the product classification exists
    const existingProductClassification = await model.findById(id)

    if (!existingProductClassification) {
      throw new ReferenceError(
        Language.translate('common:notFound', {
          name: 'product.classification',
        }),
      )
    }

    // Update the product classification
    return model.update({ id, name, description, coefficient })
  }
}
