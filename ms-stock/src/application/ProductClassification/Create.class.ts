import { Language } from '@app/application/Language'
import type { CreateProductClassificationDto } from '@app/application/ProductClassification/ProductClassification'

import { ProductClassification } from '@app/domain/database/ProductClassification'

export class Create {
  public async create({
    name,
    description,
    coefficient,
  }: CreateProductClassificationDto) {
    if (!name || !description || coefficient === undefined) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification',
        }),
      )
    }

    if (name.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.name',
        }),
      )
    }

    if (description.length < 3) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.description',
        }),
      )
    }

    if (coefficient <= 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.coefficient',
        }),
      )
    }

    const model = new ProductClassification()

    // Check if the name already exists
    const existingClassification = await model.findByName(name)

    if (existingClassification) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.classification.name',
        }),
      )
    }

    // Create the product classification
    return model.create({ name, description, coefficient })
  }
}
