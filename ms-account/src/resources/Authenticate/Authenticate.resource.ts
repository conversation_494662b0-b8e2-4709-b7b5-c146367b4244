import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Post } from '@thrift/common/engines/Server'

import {
  Generate,
  type TokenAndRefreshResult,
} from '@app/application/Authentication'
import { Language } from '@app/application/Language'

export class Authenticate {
  @Post('/authenticate')
  public async signIn({ request, response }: ResourceMethodProps) {
    try {
      response.send<TokenAndRefreshResult>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0201}`,
        ),
        data: await new Generate().generateTokenByUsernameAndPassword(
          request.body(),
        ),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0001,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_400_0001}`,
            { parameter: 'username or password' },
          ),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
