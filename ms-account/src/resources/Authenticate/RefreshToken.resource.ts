import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Post } from '@thrift/common/engines/Server'

import { Generate, type TokenResult } from '@app/application/Authentication'
import { Language } from '@app/application/Language'

export class RefreshToken {
  @Post('/authenticate/refresh')
  public async generateNewToken({ request, response }: ResourceMethodProps) {
    try {
      response.send<TokenResult>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0201}`,
        ),
        data: await new Generate().generateTokenByRefrashToken(request.body()),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      response.send<string>({
        code: ResourceMessageCode.C_401_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_401_0001}`,
          { title: 'refresh token' },
        ),
      })
    }
  }
}
