import { Debug } from '@thrift/common/engines/Debug'
import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import {
  Create,
  type CreateAccountDto,
  Delete as DeleteAccount,
  Read,
  Update,
  type UpdateAccountDto,
} from '@app/application/Account'
import { Language } from '@app/application/Language'

import type { IdentifierDto } from '@app/resources/Account/Account'

export class AccountResource {
  @Get('/accounts/:id')
  public async getAccountById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().find(id),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof NotFoundException) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', { name: 'account' }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/accounts')
  public async getAccounts({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'account',
          },
        ),
        data: await new Read().findMultiple(paginationParams),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', { name: 'account' }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Post('/accounts')
  public async postAccount({ request, response }: ResourceMethodProps) {
    try {
      const createAccountDto = request.body<CreateAccountDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'account',
          },
        ),
        data: await new Create().create(createAccountDto),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', { name: 'account' }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/accounts/:id')
  public async updateAccounts({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateAccountDto = request.body<UpdateAccountDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'account',
          },
        ),
        data: await new Update().update({
          ...updateAccountDto,
          id,
        }),
      })
    } catch (error) {
      Debug.error(error)

      if (error instanceof ReferenceError) {
        response.send({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('common:invalid', {
            name: 'account',
          }),
        })
      } else {
        response.send({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/accounts/:id')
  public async deleteAccount({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteAccount().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'account',
          },
        ),
      })
    } catch (error) {
      Debug.error(error)

      response.send({
        code: ResourceMessageCode.C_500_0500,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_500_0500}`,
        ),
      })
    }
  }
}
