import type { FetchProps } from '@app/application/Acl/Acl.d.ts'

import { Acl } from '@app/domain/database/Acl/Acl.class'

export class Read {
  public async find(id: string) {
    const model = new Acl()

    return model.find(id)
  }

  public async findMultiple({
    page,
    perPage,
    search,
    sortBy,
    sortOrder,
  }: FetchProps) {
    const model = new Acl()

    return model.findMultiple({
      page,
      perPage,
      search,
      sortBy,
      sortOrder,
    })
  }
}
