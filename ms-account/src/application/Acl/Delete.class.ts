import { Language } from '@app/application/Language'

import { Acl } from '@app/domain/database/Acl/Acl.class'

export class Delete {
  public async delete(id: string) {
    const model = new Acl()

    const existingItem = await model.find(id)

    if (!existingItem) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'acl' }),
      )
    }

    await model.delete(id)
  }
}
