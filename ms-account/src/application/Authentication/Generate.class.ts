import {
  decodeRefreshToken,
  generateRefreshToken,
  generateToken,
} from '@credentials/authorization'

import type {
  GenerateTokenProps,
  RefreshProps,
  TokenAndRefreshResult,
  TokenResult,
} from '@app/application/Authentication'

import { Account } from '@app/domain/database/Account'

export class Generate {
  public async generateTokenByUsernameAndPassword({
    username,
    password,
  }: GenerateTokenProps): Promise<TokenAndRefreshResult> {
    const model = new Account()

    const { id } = await model.findByCredentials({
      username,
      password,
    })

    const token = generateToken(id)
    const refreshToken = generateRefreshToken(id)

    return { token, refreshToken }
  }

  public async generateTokenByRefrashToken({
    refreshToken,
  }: RefreshProps): Promise<TokenResult> {
    const { ref } = decodeRefreshToken(refreshToken)
    const userId = ref as string

    return { token: generateToken(userId) }
  }
}
