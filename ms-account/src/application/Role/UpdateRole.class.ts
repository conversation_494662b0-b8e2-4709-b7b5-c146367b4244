import config from 'app.config.json'

import { Language } from '@app/application/Language'
import type { UpdateRoleDto } from '@app/application/Role'

import { Role } from '@app/domain/database/Role'

export class UpdateRole {
  public async updateRole({ id, name, description, shortname }: UpdateRoleDto) {
    if (shortname === config.roles.admin.shortname) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'role' }),
      )
    }

    const model = new Role()

    const role = await model.updateRole({
      id,
      name,
      description,
      shortname,
    })

    return role
  }
}
