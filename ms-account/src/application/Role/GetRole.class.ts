import type { FetchRolesProps } from '@app/application/Role'

import { Role } from '@app/domain/database/Role'

export class GetRole {
  public async findRole(id: string) {
    const model = new Role()

    return model.findRole(id)
  }

  public async findRoles({
    page,
    perPage,
    search,
    sortBy,
    sortOrder,
  }: FetchRolesProps) {
    const model = new Role()

    return model.findRoles({
      page,
      perPage,
      search,
      sortBy,
      sortOrder,
    })
  }
}
