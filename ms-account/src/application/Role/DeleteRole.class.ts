import config from 'app.config.json'

import { Language } from '@app/application/Language'

import { Role } from '@app/domain/database/Role'

export class DeleteRole {
  public async deleteRole(id: string) {
    const model = new Role()

    const existingRole = await model.findRole(id)

    if (!existingRole) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'role' }),
      )
    }

    if (existingRole.shortname === config.roles.admin.shortname) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'role' }),
      )
    }

    await model.deleteRole(id)
  }
}
