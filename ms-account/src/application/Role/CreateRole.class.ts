import { Language } from '@app/application/Language'
import type { CreateRoleDto } from '@app/application/Role'

import { Role } from '@app/domain/database/Role'

export class CreateRole {
  public async createRole({ name, description, shortname }: CreateRoleDto) {
    if (
      !name ||
      name.length < 3 ||
      !shortname ||
      shortname.length < 3 ||
      !description ||
      description.length < 3
    ) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'role' }),
      )
    }

    const model = new Role()

    const role = await model.createRole({
      name,
      description,
      shortname,
    })

    return role
  }
}
