import type { FetchProps } from '@app/application/Account/Account.d.ts'

import { Account } from '@app/domain/database/Account/Account.class'

export class Read {
  public async find(id: string) {
    const model = new Account()

    return model.findById(id)
  }

  public async findMultiple({
    page,
    perPage,
    search,
    sortBy,
    sortOrder,
  }: FetchProps) {
    const model = new Account()

    return model.find({
      page,
      perPage,
      search,
      sortBy,
      sortOrder,
    })
  }
}
