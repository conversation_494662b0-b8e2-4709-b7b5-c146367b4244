import type { UpdateAccountDto } from '@app/application/Account/Account'
import { Language } from '@app/application/Language'

import { Account } from '@app/domain/database/Account/Account.class'
import { Role } from '@app/domain/database/Role'

export class Update {
  public async update({
    id,
    username,
    password,
    isActive,
    roleId,
  }: UpdateAccountDto) {
    const model = new Account()

    // Check if the account exists
    const existingAccount = await model.findById(id)

    if (!existingAccount) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    // Check if the username already exists
    const existingUsername = await model.findByUsername(username)

    if (existingUsername && existingUsername.id !== id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'account.username' }),
      )
    }

    // Check if the roleId exists
    const roleModel = new Role()
    const existingRole = await roleModel.findRole(roleId)

    if (!existingRole) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'account.roleId' }),
      )
    }

    // Check if the password is empty
    if (!password || password.trim() === '') {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'account.password' }),
      )
    }

    const item = await model.update({
      id,
      username,
      password,
      isActive,
      roleId,
    })

    return item
  }
}
