import { Language } from '@app/application/Language'

import { Account } from '@app/domain/database/Account'

export class Delete {
  public async delete(id: string) {
    const model = new Account()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    await model.delete(id)
  }
}
