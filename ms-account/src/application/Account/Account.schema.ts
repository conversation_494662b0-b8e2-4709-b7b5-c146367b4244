import { z } from 'zod'

export const CreateAccountSchema = z.object({
  username: z.string().min(3),
  password: z.string().min(3),
  isActive: z.boolean().optional(),
  roleId: z.string().min(3),
  name: z.string().min(3),
  birthDate: z.coerce.date(),
  gender: z.enum(['male', 'female', 'other']),
})

export const UpdateAccountSchema = CreateAccountSchema.extend({
  id: z.string().uuid(),
  username: z.string().min(3).optional(),
  password: z.string().min(3).optional(),
  isActive: z.boolean().optional(),
  roleId: z.string().min(3).optional(),
  name: z.string().min(3).optional(),
  birthDate: z.coerce.date().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
})
