import { Language } from '@app/application/Language'

import { Account } from '@app/domain/database/Account'

export class Activation {
  public async activate(id: string) {
    const model = new Account()

    const existingAccount = await model.findById(id)

    if (!existingAccount) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    await model.activate(id)
  }

  public async deactivate(id: string) {
    const model = new Account()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    await model.deactivate(id)
  }
}
