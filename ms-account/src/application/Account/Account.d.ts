import type { PaginationParams } from '@thrift/common/engines/Pagination'

import type {
  CreateAccountSchema,
  UpdateAccountSchema,
} from '@app/application/Account/Account.schema'

export type FetchProps = PaginationParams

export type FetchItemProps = { id: string }

export type CreateAccountDto = z.infer<typeof CreateAccountSchema>

export type UpdateAccountDto = z.infer<typeof UpdateAccountSchema>
