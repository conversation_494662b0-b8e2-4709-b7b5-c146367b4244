import { Language } from '@app/application/Language'
import type { PersonCreateDto, PersonResponse } from '@app/application/Person'

import { Person as PersonDomain } from '@app/domain/database/Person'

export class Person {
  private personDomain = new PersonDomain()

  public async create({
    name,
    birthDate,
    gender,
  }: PersonCreateDto): Promise<{ id: string }> {
    if (!name || name.trim().length === 0) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'person.name' }),
      )
    }

    // Create a person with the provided data
    const personId = await this.personDomain.create({
      name,
      birthDate,
      gender,
    })

    return { id: personId }
  }

  public async findById(id: string): Promise<PersonResponse> {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'person.id' }),
      )
    }

    return this.personDomain.findById(id)
  }
}
