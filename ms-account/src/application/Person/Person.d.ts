export type PersonCreateDto = {
  name: string
  birthDate: Date
  gender: string
}

export type PersonNaturalModel = {
  id: string
  name: string
  birthDate: Date
  gender: string
  personId: string
  createdAt: Date
  updatedAt: Date
}

export type PersonLegalModel = {
  id: string
  personId: string
  fictitiousName: string
  legalName: string
  createdAt: Date
  updatedAt: Date
}

export type PersonResponse = {
  data: PersonNaturalModel | PersonLegalModel
}
