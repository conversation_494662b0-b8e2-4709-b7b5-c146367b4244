import { type Acl as AclModel, PrismaClient } from '@prisma/client'
import i18next from 'i18next'

import type { PaginatedResponse } from '@thrift/common/engines/Pagination'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateDto,
  FetchMultipleProps,
  UpdateDto,
} from '@app/domain/database/Acl/Acl.d'

export class Acl {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async find(
    id: string,
  ): Promise<Pick<AclModel, 'id' | 'name' | 'tagname' | 'description'>> {
    const acl = await this.prisma.acl.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        tagname: true,
        description: true,
      },
    })

    if (!acl) {
      throw new NotFoundException()
    }

    return acl
  }

  public async findMultiple({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    ids = [],
  }: FetchMultipleProps): Promise<PaginatedResponse<Partial<AclModel>>> {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(ids.length > 0 && { id: { in: ids } }),
      ...(search && {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
          { tagname: { contains: search } },
        ],
      }),
    }

    const [items, totalItems] = await this.prisma.$transaction([
      this.prisma.acl.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: { id: true, tagname: true, description: true, name: true },
      }),
      this.prisma.acl.count({ where }),
    ])

    return {
      items,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create(
    acl: CreateDto,
  ): Promise<Pick<AclModel, 'id' | 'tagname' | 'description' | 'name'>> {
    const currentAcl = await this.prisma.acl.findUnique({
      where: {
        tagname: acl.tagname,
      },
    })

    if (currentAcl) {
      throw new ReferenceError(
        i18next.t('acl:error.invalid.tagname', {
          tagname: acl.tagname,
        }),
      )
    }

    return this.prisma.acl.create({
      data: acl,
      select: {
        id: true,
        tagname: true,
        name: true,
        description: true,
      },
    })
  }

  public async update(
    updateDto: Partial<UpdateDto>,
  ): Promise<Pick<AclModel, 'tagname' | 'description' | 'name'>> {
    const { id } = updateDto

    const currentAcl = await this.prisma.acl.findUnique({
      where: { id },
    })

    if (!currentAcl) {
      throw new ReferenceError(i18next.t('common:notFound', { name: 'acl' }))
    }

    if (updateDto.tagname && updateDto.tagname !== currentAcl.tagname) {
      const hasTagname = await this.prisma.acl.findUnique({
        where: { tagname: updateDto.tagname },
      })

      if (hasTagname) {
        throw new ReferenceError(
          i18next.t('acl:error.invalid.tagname', {
            tagname: updateDto.tagname,
          }),
        )
      }
    }

    return this.prisma.acl.update({
      where: { id },
      data: updateDto,
      select: {
        tagname: true,
        name: true,
        description: true,
      },
    })
  }

  public async delete(id: string) {
    await this.prisma.acl.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
