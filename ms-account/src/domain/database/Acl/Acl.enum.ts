export enum Acl {
  ACCOUNT_AUTHENTICATE_CREATE = 'account:authenticate:create',
  ACCOUNT_AUTHENTICATE_DELETE = 'account:authenticate:delete',

  ACCOUNT_USER_CREATE = 'account:user:create',
  ACCOUNT_USER_READ = 'account:user:read',
  ACCOUNT_USER_UPDATE = 'account:user:update',
  ACCOUNT_USER_DELETE = 'account:user:delete',

  ACCOUNT_ROLE_CREATE = 'account:role:create',
  ACCOUNT_ROLE_READ = 'account:role:read',
  ACCOUNT_ROLE_UPDATE = 'account:role:update',
  ACCOUNT_ROLE_DELETE = 'account:role:delete',

  ACCOUNT_ACL_CREATE = 'account:acl:create',
  ACCOUNT_ACL_READ = 'account:acl:read',
  ACCOUNT_ACL_UPDATE = 'account:acl:update',
  ACCOUNT_ACL_DELETE = 'account:acl:delete',
}
