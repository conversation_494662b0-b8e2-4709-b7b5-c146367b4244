import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

import { Acl } from '@app/domain/database/Acl/Acl.enum'

const prisma = new PrismaClient()

;(async function () {
  const permissions = [
    {
      name: '<PERSON>ria<PERSON> Usuário',
      tagname: Acl.ACCOUNT_USER_CREATE,
      description: 'Permissão para criar usuários',
    },
    {
      name: '<PERSON>r Usuário',
      tagname: Acl.ACCOUNT_USER_READ,
      description: 'Permissão para visualizar usuários',
    },
    {
      name: 'Atualizar Usuário',
      tagname: Acl.ACCOUNT_USER_UPDATE,
      description: 'Permissão para atualizar usuários',
    },
    {
      name: 'Deletar Usuário',
      tagname: Acl.ACCOUNT_USER_DELETE,
      description: 'Permissão para deletar usuários',
    },
    {
      name: '<PERSON>ria<PERSON>',
      tagname: Acl.ACCOUNT_ROLE_CREATE,
      description: 'Permissão para criar perfis',
    },
    {
      name: 'Ler Per<PERSON>l',
      tagname: Acl.ACCOUNT_ROLE_READ,
      description: 'Permissão para visualizar perfis',
    },
    {
      name: 'Atualizar Perfil',
      tagname: Acl.ACCOUNT_ROLE_UPDATE,
      description: 'Permissão para atualizar perfis',
    },
    {
      name: 'Deletar Perfil',
      tagname: Acl.ACCOUNT_ROLE_DELETE,
      description: 'Permissão para deletar perfis',
    },
    {
      name: 'Criar Permissão',
      tagname: Acl.ACCOUNT_ACL_CREATE,
      description: 'Permissão para criar permissões',
    },
    {
      name: 'Ler Permissão',
      tagname: Acl.ACCOUNT_ACL_READ,
      description: 'Permissão para visualizar permissões',
    },
    {
      name: 'Atualizar Permissão',
      tagname: Acl.ACCOUNT_ACL_UPDATE,
      description: 'Permissão para atualizar permissões',
    },
    {
      name: 'Deletar Permissão',
      tagname: Acl.ACCOUNT_ACL_DELETE,
      description: 'Permissão para deletar permissões',
    },
  ]

  for (const permission of permissions) {
    await prisma.acl.upsert({
      where: { tagname: permission.tagname },
      update: {},
      create: {
        name: permission.name,
        tagname: permission.tagname,
        description: permission.description,
      },
    })
  }
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
