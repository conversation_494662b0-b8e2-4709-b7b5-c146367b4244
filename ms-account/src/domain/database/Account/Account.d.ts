import type { PaginationParams } from '@thrift/common/engines/Pagination'

export type CredentialsProps = {
  username: string
  password: string
}

export type FetchAccountsProps = PaginationParams

export type CreateAccountDto = {
  username: string
  password: string
  isActive?: boolean
  roleId: string
  personId?: string
  // Person natural data
  name?: string
  birthDate?: Date
  gender?: string
  nickname?: string
}

export type UpdateAccountDto = CreateAccountDto & {
  id: string
}
