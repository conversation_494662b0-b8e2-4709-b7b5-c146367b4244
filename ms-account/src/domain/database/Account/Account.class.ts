import { PrismaClient } from '@prisma/client'
import { compare, hashSync } from 'bcryptjs'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateAccountDto,
  CredentialsProps,
  FetchAccountsProps,
  UpdateAccountDto,
} from '@app/domain/database/Account'
import i18next from '@app/domain/locales/i18n/i18next'

export class Account {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findByCredentials({ username, password }: CredentialsProps) {
    const account = await this.prisma.account.findFirst({
      where: { username, deleted: false },
    })

    if (!account) {
      throw new ReferenceError(
        i18next.t('common:notFound', { name: 'username' }),
      )
    }

    if (!(await compare(password, account.password))) {
      throw new ReferenceError(
        i18next.t('database:invalid', { name: 'password' }),
      )
    }

    return account
  }

  public async findById(id: string) {
    const account = await this.prisma.account.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        username: true,
        isActive: true,
        role: {
          select: {
            id: true,
            name: true,
            shortname: true,
          },
        },
      },
    })

    if (!account) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'account' }),
      )
    }

    return account
  }

  public async findByUsername(username: string) {
    return this.prisma.account.findFirst({
      where: { username, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchAccountsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ username: { contains: search } }],
      }),
    }

    const [accounts, totalItems] = await this.prisma.$transaction([
      this.prisma.account.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          username: true,
          isActive: true,
          role: {
            select: {
              id: true,
              name: true,
              shortname: true,
            },
          },
        },
      }),
      this.prisma.account.count({ where }),
    ])

    return {
      items: accounts,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    username,
    password,
    isActive,
    roleId,
    personId,
  }: CreateAccountDto) {
    const account = await this.prisma.account.create({
      data: {
        username,
        password: hashSync(password, 10),
        isActive,
        roleId,
        personId,
      },
      select: {
        id: true,
        username: true,
        isActive: true,
        personId: true,
        role: {
          select: {
            id: true,
            name: true,
            shortname: true,
          },
        },
      },
    })

    return account
  }

  public async update({
    id,
    username,
    password,
    isActive,
    roleId,
  }: UpdateAccountDto) {
    const account = await this.prisma.account.update({
      where: { id },
      data: {
        username,
        password: hashSync(password, 10),
        isActive,
        roleId,
      },
    })

    return account
  }

  public async delete(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { deleted: true },
    })

    return account
  }

  public async deactivate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: false },
    })

    return account
  }

  public async activate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: true },
    })

    return account
  }
}
