export type PersonNaturalModel = {
  id: string
  personId: string
  name: string
  gender: string
  birthDate: Date
  createdAt: Date
  updatedAt: Date
}

export type PersonNaturalResponse = {
  data: PersonNaturalModel
}

export type PersonLegalModel = {
  id: string
  personId: string
  fictitiousName: string
  legalName: string
  createdAt: Date
  updatedAt: Date
}

export type PersonResponse = {
  data: PersonNaturalModel | PersonLegalModel
}

export type PersonCreateProps = {
  name: string
}

export type PersonNaturalCreateProps = {
  name: string
  birthDate?: Date
  gender?: string
  personId?: string
}
