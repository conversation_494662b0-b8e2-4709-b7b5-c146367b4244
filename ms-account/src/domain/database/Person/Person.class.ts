import { personServiceClient } from '@app/config/microservices'

import { Debug } from '@thrift/common/engines/Debug'

import type {
  PersonNaturalCreateProps,
  PersonNaturalResponse,
  PersonResponse,
} from '@app/domain/database/Person/Person.d'
import i18next from '@app/domain/locales/i18n/i18next'

export class Person {
  private client = personServiceClient

  public async create(props: PersonNaturalCreateProps): Promise<string> {
    try {
      const naturalPersonResponse =
        await this.client.post<PersonNaturalResponse>('/person/natural', {
          name: props.name,
          birthDate: props.birthDate,
          gender: props.gender,
        })

      if (!naturalPersonResponse.data) {
        throw new ReferenceError(i18next.t('database:invalidData'))
      }

      return naturalPersonResponse.data.data.personId
    } catch (error) {
      Debug.error({ message: 'Error creating person', error })
      throw new ReferenceError(i18next.t('database:invalidData'))
    }
  }

  public async findById(id: string): Promise<PersonResponse> {
    try {
      const response = await this.client.get<PersonResponse>(`/person/${id}`)

      if (!response.data) {
        throw new ReferenceError(
          i18next.t('common:notFound', { name: 'person' }),
        )
      }

      return response.data
    } catch (error) {
      Debug.error({ message: 'Error finding person', error })
      throw new ReferenceError(i18next.t('common:notFound', { name: 'person' }))
    }
  }
}
