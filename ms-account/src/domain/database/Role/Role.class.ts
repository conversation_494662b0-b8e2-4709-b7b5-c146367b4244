import { PrismaClient, type Role as RoleModel } from '@prisma/client'
import i18next from 'i18next'

import type { PaginatedResponse } from '@thrift/common/engines/Pagination'
import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  AssignAclsToRoleDto,
  CreateRoleDto,
  FetchRolesProps,
  RoleResponse,
  UpdateRoleDto,
} from '@app/domain/database/Role/Role.d'

export class Role {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findRole(id: string): Promise<RoleResponse> {
    return this.prisma.role.findUnique({
      where: { id },
      select: {
        id: true,
        shortname: true,
        name: true,
        description: true,
        acls: {
          select: {
            id: true,
            tagname: true,
            description: true,
            name: true,
          },
        },
      },
    })
  }

  public async findRoles({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchRolesProps): Promise<PaginatedResponse<Partial<RoleModel>>> {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
          { shortname: { contains: search } },
        ],
      }),
    }

    const [roles, totalItems] = await this.prisma.$transaction([
      this.prisma.role.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: { id: true, shortname: true, description: true, name: true },
      }),
      this.prisma.role.count({ where }),
    ])

    return {
      items: roles,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async clearAcls(roleId: string): Promise<void> {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      select: { id: true },
    })

    if (!role) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'role' }),
      )
    }

    await this.prisma.role.update({
      where: { id: roleId },
      data: { acls: { set: [] } },
    })
  }

  public async assignAcls({ roleId, aclIds }: AssignAclsToRoleDto) {
    await this.prisma.role.update({
      where: { id: roleId },
      data: {
        acls: {
          set: aclIds.map((aclId) => ({ id: aclId })),
        },
      },
    })
  }

  public async createRole(
    role: CreateRoleDto,
  ): Promise<Pick<RoleModel, 'id' | 'shortname' | 'description' | 'name'>> {
    const existingRole = await this.prisma.role.findUnique({
      where: {
        shortname: role.shortname,
      },
    })

    if (existingRole) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'role',
          field: 'shortname',
        }),
      )
    }

    return this.prisma.role.create({
      data: role,
      select: {
        id: true,
        shortname: true,
        name: true,
        description: true,
      },
    })
  }

  public async updateRole(
    updateRoleDto: Partial<UpdateRoleDto>,
  ): Promise<Pick<RoleModel, 'shortname' | 'description' | 'name'>> {
    const { id } = updateRoleDto

    const existingRole = await this.prisma.role.findUnique({
      where: { id },
    })

    if (!existingRole) {
      throw new ReferenceError(i18next.t('common:notFound', { name: 'role' }))
    }

    if (
      updateRoleDto.shortname &&
      updateRoleDto.shortname !== existingRole.shortname
    ) {
      const shortnameExists = await this.prisma.role.findUnique({
        where: { shortname: updateRoleDto.shortname },
      })

      if (shortnameExists) {
        throw new ReferenceError(
          i18next.t('common:conflict', {
            entity: 'role',
            field: 'shortname',
          }),
        )
      }
    }

    return this.prisma.role.update({
      where: { id },
      data: updateRoleDto,
      select: {
        shortname: true,
        name: true,
        description: true,
      },
    })
  }

  public async deleteRole(id: string) {
    await this.prisma.role.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
