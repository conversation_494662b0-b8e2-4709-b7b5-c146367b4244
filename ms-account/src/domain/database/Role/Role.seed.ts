import { PrismaClient } from '@prisma/client'
import configs from 'app.config.json'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  const roles = [
    {
      shortname: configs.roles.admin.shortname,
      name: configs.roles.admin.name,
      description: configs.roles.admin.description,
      aclTags: [],
    },
  ]

  for (const role of roles) {
    await prisma.role.upsert({
      where: { shortname: role.shortname },
      update: {},
      create: {
        shortname: role.shortname,
        name: role.name,
        description: role.description,
      },
    })
  }
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
