import { readFileSync } from 'fs'
import jwt from 'jsonwebtoken'
import { resolve } from 'path'

import {
  ResourceCrypto,
  ResourceExpiresIn,
} from '@thrift/common/engines/Resource'

const _publicKey = readFileSync(resolve(__dirname, '../public_key.pem'), 'utf8')
const _privateKey = readFileSync(
  resolve(__dirname, '../private_key.pem'),
  'utf8',
)

export const generateToken = (id: string): string => {
  return jwt.sign({}, _privateKey, {
    algorithm: ResourceCrypto.ALGORITHM,
    expiresIn: ResourceExpiresIn.TOKEN,
    subject: id,
  })
}

export const decodeToken = (token: string): jwt.JwtPayload => {
  const decode = jwt.verify(token, _publicKey, {
    algorithms: [ResourceCrypto.ALGORITHM],
  })

  if (
    typeof decode !== 'string' &&
    decode?.ref !== undefined &&
    (!decode?.sub?.length || !decode?.exp?.toString().length)
  ) {
    throw new ReferenceError('Invalid token')
  }

  return decode as jwt.JwtPayload
}

export const verifyToken = (bearerToken: string): boolean => {
  try {
    const [, token] = bearerToken.split(' ')

    decodeToken(token as string)

    return true
  } catch {
    return false
  }
}

export const generateRefreshToken = (id: string): string => {
  return jwt.sign({ ref: id }, _privateKey, {
    algorithm: ResourceCrypto.ALGORITHM,
    expiresIn: ResourceExpiresIn.REFRESH_TOKEN,
    subject: id,
  })
}

export const decodeRefreshToken = (refreshToken: string): jwt.JwtPayload => {
  const decode = jwt.verify(refreshToken, _publicKey, {
    algorithms: [ResourceCrypto.ALGORITHM],
  })

  if (
    typeof decode !== 'string' &&
    decode?.ref === undefined &&
    (!decode?.sub?.length || !decode?.exp?.toString().length)
  ) {
    throw new ReferenceError('Invalid token')
  }

  return decode as jwt.JwtPayload
}

export const verifyRefreshToken = (refreshToken: string): boolean => {
  try {
    decodeToken(refreshToken)

    return true
  } catch {
    return false
  }
}
