import { verifyToken } from '@credentials/authorization'
import resources from '@credentials/resources.json'

import {
  ResourceMessageCode,
  type ResourceMiddlewareMethodProps,
} from '@thrift/common/engines/Resource'

export const authorization = ({
  request,
  response,
  next,
}: ResourceMiddlewareMethodProps): void => {
  try {
    const { authorization } = request.headers()

    if (resources.public.includes(request.url())) return next()

    if (!authorization || !authorization.startsWith('Bearer'))
      throw new ReferenceError('Unauthorized! Authorization header is required')

    if (!verifyToken(authorization))
      throw new ReferenceError('Unauthorized! Invalid token')

    return next()
  } catch (error) {
    response.send({
      code: ResourceMessageCode.C_401_0401,
      message: error instanceof Error ? error.message : 'Unauthorized',
      data: error,
    })

    return next(error)
  }
}
