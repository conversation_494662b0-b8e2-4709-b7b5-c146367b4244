{"deployment": {"type": "api", "resource": "account"}, "server": {"host": "localhost", "port": 8085, "charset": "UTF-8", "language": "pt"}, "database": {"driver": "mysql", "username": "root", "password": "root", "host": "localhost", "port": "3306", "schema": "account"}, "microservices": {"person": {"baseUrl": "http://localhost:8081"}}, "roles": {"admin": {"shortname": "admin", "name": "Admin", "description": "System Admin"}}}