{"name": "@thrift/account", "private": true, "version": "1.0.0", "description": "Account application service", "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-account.git"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": "yarn healthcheck && tsx watch src/main.ts", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "start": "node dist/src/main.js"}, "devDependencies": {"@types/bcryptjs": "3.0.0", "@types/jsonwebtoken": "9.0.9", "@types/node": "22.15.21", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-unused-imports": "4.1.4", "prettier": "3.5.3", "prisma": "6.8.2", "tsc-alias": "1.8.16", "tsx": "4.19.4", "typescript": "5.8.3", "typescript-eslint": "8.32.1"}, "dependencies": {"@prisma/client": "6.8.2", "@thrift/common": "https://github.com/thrift-technology/ms-common.git#main", "bcryptjs": "3.0.2", "i18next": "25.2.0", "i18next-browser-languagedetector": "8.1.0", "jsonwebtoken": "9.0.2"}, "engines": {"node": ">=20 <24", "npm": ">=10 <12", "yarn": ">=1.22 <2"}, "prisma": {"schema": "./prisma/schema"}}