{"name": "@thrift/common", "private": true, "version": "0.1.0", "description": "Common base tools shared between the applications", "author": "Thrift Technology <<EMAIL>>", "types": "./engines/index.d.ts", "main": "./engines/index.js", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-common.git"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "typecheck": "tsc --noEmit", "healthcheck": "yarn lint && yarn format && yarn typecheck", "prepare": "husky"}, "devDependencies": {"@types/express": "5.0.2", "@types/node": "22.15.20", "husky": "^9.1.7", "tsc-alias": "1.8.16", "typescript": "5.8.3"}, "dependencies": {"@eslint/js": "9.27.0", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@tsconfig/recommended": "1.0.8", "axios": "1.9.0", "dotenv": "16.5.0", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-unused-imports": "4.1.4", "express": "5.1.0", "prettier": "3.5.3", "typescript-eslint": "8.32.1", "zod": "3.25.7"}, "engines": {"node": ">=20 <25", "npm": ">=10 <12", "yarn": ">=1.22 <2"}}