const affectAppPackage = require('./.eslint.config/app/config.js')
const affectDomainPackage = require('./.eslint.config/app/domain/config.js')
const affectResourcesPackage = require('./.eslint.config/app/resources/config.js')
const affectDefaultPackages = require('./.eslint.config/default/config.js')
const js = require('@eslint/js')
const eslintPluginPrettier = require('eslint-plugin-prettier')
const eslintPluginPrettierRecommended = require('eslint-plugin-prettier/recommended')
const unusedImports = require('eslint-plugin-unused-imports')
const tseslint = require('typescript-eslint')

const defautConfig = tseslint.config(
  { ignores: ['coverage', 'dist', 'node_modules', 'storybook-static'] },
  {
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      eslintPluginPrettierRecommended,
    ],
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2023,
    },
    plugins: {
      'unused-imports': unusedImports,
      prettier: eslintPluginPrettier,
    },
  },
)

module.exports = [
  ...defautConfig,
  ...affectDefaultPackages,
  ...affectAppPackage,
  ...affectDomainPackage,
  ...affectResourcesPackage,
]
