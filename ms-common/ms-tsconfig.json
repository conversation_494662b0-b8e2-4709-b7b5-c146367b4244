{"extends": "@tsconfig/recommended/tsconfig.json", "compilerOptions": {"declaration": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "noImplicitAny": false, "noImplicitReturns": true}, "exclude": ["**/dist", "**/engines", "**/build", "**/fixtures", "**/node_modules", "**/__tests__", "**/__mocks__", "**/__fixtures__", "**/__snapshots__", "**/coverage", "**/docs", "**/examples", "**/test", "**/tests", "**/tmp"]}