import { type DotenvPopulateInput, populate } from 'dotenv'

import type { AppConfigDatabaseProps } from '@thrift/common/engines/Environment'

export class Database {
  public static setDatabaseUrl({
    driver,
    username,
    password,
    host,
    port,
    schema,
  }: AppConfigDatabaseProps): void {
    const { DATABASE_STRING_CONNECT } = process.env

    if (DATABASE_STRING_CONNECT)
      populate(process.env as DotenvPopulateInput, {
        DATABASE_URL: `${DATABASE_STRING_CONNECT}/${schema}`,
      })
    else
      populate(process.env as DotenvPopulateInput, {
        DATABASE_URL: `${driver}://${username}:${password}@${host}:${port}/${schema}`,
      })
  }
}
