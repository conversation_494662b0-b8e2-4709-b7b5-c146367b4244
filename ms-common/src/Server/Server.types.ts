import type { ResourceMethodProps } from '@thrift/common/engines/Resource'

export type ServerStartMethodProps = {
  pathRoot: string
  host: string
  port: string | number
  charset: string
  language: string
}

export type ServerRouterMethodProps = {
  endpoint: string
  callback: (props: ResourceMethodProps) => void
}

export type ServerRouterMethods = {
  get: (props: ServerRouterMethodProps) => void
  post: (props: ServerRouterMethodProps) => void
  put: (props: ServerRouterMethodProps) => void
  patch: (props: ServerRouterMethodProps) => void
  delete: (props: ServerRouterMethodProps) => void
}
