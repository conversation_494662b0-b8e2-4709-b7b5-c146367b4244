import { AsyncLocalStorage } from 'async_hooks'

import { Debug } from '@thrift/common/engines/Debug'
import type { RequestContext } from '@thrift/common/engines/Http'

const asyncLocalStorage = new AsyncLocalStorage<RequestContext>()

let manualToken: string | undefined = undefined

export const requestContextMiddleware = (req, res, next) => {
  const requestContext: RequestContext = {
    headers: { ...req.headers },
  }

  asyncLocalStorage.run(requestContext, () => {
    next()
  })
}

export const getRequestContext = (): RequestContext | undefined => {
  return asyncLocalStorage.getStore()
}

export const getAuthorizationToken = (): string | undefined => {
  if (manualToken) {
    return manualToken
  }

  const context = getRequestContext()

  if (!context) return undefined

  const authHeader = context.headers.authorization as string | undefined

  if (!authHeader) return undefined

  const match = authHeader.match(/^Bearer\s+(.*)$/)

  return match ? match[1] : undefined
}

export const setAuthorizationToken = (token: string): void => {
  Debug.info({ message: 'Manually setting authorization token' })
  manualToken = token
}

export const clearAuthorizationToken = (): void => {
  Debug.info({ message: 'Clearing manually set authorization token' })
  manualToken = undefined
}
