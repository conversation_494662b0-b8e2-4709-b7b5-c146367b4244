import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
} from 'axios'

import { Debug } from '@thrift/common/engines/Debug'
import type { HttpClientConfig } from '@thrift/common/engines/Http'
import { getAuthorizationToken } from '@thrift/common/engines/Http/RequestContext'

export class HttpClient {
  private instance: AxiosInstance

  constructor(config: HttpClientConfig) {
    this.instance = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...config,
    })

    this.instance.interceptors.request.use(
      (config) => {
        const token = getAuthorizationToken()

        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`
        }

        Debug.info({
          message: `HTTP Request: ${config.method?.toUpperCase()} ${config.url}`,
        })

        return config
      },
      (error) => {
        Debug.error({
          message: 'HTTP Request Error',
          error,
        })

        return Promise.reject(error)
      },
    )

    this.instance.interceptors.response.use(
      (response) => {
        Debug.info({
          message: `HTTP Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`,
        })

        return response
      },
      (error) => {
        Debug.error({
          message: 'HTTP Response Error',
          error: error.response
            ? {
                status: error.response.status,
                data: error.response.data,
                url: error.config.url,
                method: error.config.method,
              }
            : error,
        })

        // @TODO: When the error is 401, we should refresh the token
        return Promise.reject(error)
      },
    )
  }

  public async get<T = unknown>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return this.instance.get<T>(url, config)
  }

  public async post<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, data, config)
  }

  public async put<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config)
  }

  public async delete<T = unknown>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config)
  }

  public async patch<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return this.instance.patch<T>(url, data, config)
  }
}
