import {
  HttpClient,
  type HttpClientConfig,
  type MicroserviceConfig,
} from '@thrift/common/engines/Http'

export class MicroserviceClient extends HttpClient {
  private serviceName: string

  constructor(config: MicroserviceConfig) {
    const { serviceName, ...httpConfig } = config

    super(httpConfig as HttpClientConfig)
    this.serviceName = serviceName
  }

  public getServiceName(): string {
    return this.serviceName
  }
}
