# Pagination and Filtering

This module provides standardized pagination, filtering, and sorting functionality for use across microservices.

## Components

- `PaginationParams` - Interface for pagination parameters
- `PaginationMeta` - Interface for pagination metadata
- `PaginatedResponse<T>` - Interface for paginated responses
- `PaginationFilterSchema` - Zod schema for common pagination and filtering
- `createFilterSchema()` - Helper function to create resource-specific filter schemas

## Usage Examples

### Basic Usage

```typescript
import { validateWithZod } from '@thrift/common/engines/Validation/validateWithZod'
import { PaginationFilterSchema, type PaginationFilterDto } from '@thrift/common/engines/Pagination'

// Validate request query parameters
const filter: PaginationFilterDto = validateWithZod(PaginationFilterSchema, req.query)

// Use the validated parameters
const { page, perPage, sortBy, sortOrder, search } = filter
```

### Creating Resource-Specific Filter Schemas

```typescript
import { z } from 'zod'
import { createFilterSchema } from '@thrift/common/engines/Pagination'

// Create a resource-specific filter schema
export const FilterTenantSchema = createFilterSchema({
  // Add tenant-specific filter fields
  tenantUid: z.string().optional(),
  balance: z.number().optional(),
})

// Type inference works as expected
export type FilterTenantDto = z.infer<typeof FilterTenantSchema>
```

### Using in Domain Layer

```typescript
import type { PaginationParams } from '@thrift/common/engines/Pagination'

export type FetchTenantsProps = PaginationParams & {
  id?: string
  tenantUid?: string
}

// In your class method
public async find({
  page = 1,
  perPage = 10,
  search,
  sortBy = 'id',
  sortOrder = 'asc',
  id,
  tenantUid,
}: FetchTenantsProps) {
  // Implementation
}
```
