"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Delete = exports.Patch = exports.Put = exports.Post = exports.Get = void 0;
const Debug_1 = require("../Debug");
const Server_1 = require("../Server");
const register = (method, endpoint) => {
    return (target, propertyKey, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    descriptor) => {
        Server_1.Server.router[method]({
            endpoint,
            callback: (props) => descriptor.value(props),
        });
        Debug_1.Debug.info({
            message: `"${method}:${endpoint}" has been routing successfully`,
        });
    };
};
const Get = (endpoint) => register(Server_1.ServerMethod.GET, endpoint);
exports.Get = Get;
const Post = (endpoint) => register(Server_1.ServerMethod.POST, endpoint);
exports.Post = Post;
const Put = (endpoint) => register(Server_1.ServerMethod.PUT, endpoint);
exports.Put = Put;
const Patch = (endpoint) => register(Server_1.ServerMethod.PATCH, endpoint);
exports.Patch = Patch;
const Delete = (endpoint) => register(Server_1.ServerMethod.DELETE, endpoint);
exports.Delete = Delete;
//# sourceMappingURL=Server.decorator.js.map