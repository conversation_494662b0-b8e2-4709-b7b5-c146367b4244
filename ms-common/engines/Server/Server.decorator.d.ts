export declare const Get: (endpoint: string) => (target: object, propertyKey: string, descriptor: TypedPropertyDescriptor<any>) => void;
export declare const Post: (endpoint: string) => (target: object, propertyKey: string, descriptor: TypedPropertyDescriptor<any>) => void;
export declare const Put: (endpoint: string) => (target: object, propertyKey: string, descriptor: TypedPropertyDescriptor<any>) => void;
export declare const Patch: (endpoint: string) => (target: object, propertyKey: string, descriptor: TypedPropertyDescriptor<any>) => void;
export declare const Delete: (endpoint: string) => (target: object, propertyKey: string, descriptor: TypedPropertyDescriptor<any>) => void;
