"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = exports.ServerRouter = void 0;
var express_1 = require("express");
Object.defineProperty(exports, "ServerRouter", { enumerable: true, get: function () { return express_1.Router; } });
var Server_class_1 = require("../Server/Server.class");
Object.defineProperty(exports, "Server", { enumerable: true, get: function () { return Server_class_1.Server; } });
__exportStar(require("../Server/Server.decorator"), exports);
__exportStar(require("../Server/Server.enum"), exports);
//# sourceMappingURL=index.js.map