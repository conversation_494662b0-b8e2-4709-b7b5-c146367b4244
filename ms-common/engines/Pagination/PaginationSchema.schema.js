"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationFilterSchema = void 0;
exports.createFilterSchema = createFilterSchema;
const zod_1 = require("zod");
/**
 * Common pagination and filtering schema for use across microservices
 * This schema provides standardized filtering, sorting, and pagination parameters
 * that can be extended by specific resource schemas
 */
exports.PaginationFilterSchema = zod_1.z.object({
    // Filtering
    id: zod_1.z.string().uuid().optional(),
    search: zod_1.z.string().optional(),
    // Pagination
    page: zod_1.z
        .union([zod_1.z.string(), zod_1.z.number()])
        .optional()
        .transform((val) => (val ? Number(val) : undefined)),
    perPage: zod_1.z
        .union([zod_1.z.string(), zod_1.z.number()])
        .optional()
        .transform((val) => (val ? Number(val) : undefined)),
    // Sorting
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
});
/**
 * Helper function to create a resource-specific filter schema
 * by extending the base pagination filter schema
 *
 * @param schema - The resource-specific schema to extend the base schema with
 * @returns A new schema that includes both pagination and resource-specific filters
 */
function createFilterSchema(schema) {
    return exports.PaginationFilterSchema.extend(schema);
}
//# sourceMappingURL=PaginationSchema.schema.js.map