{"version": 3, "file": "express.module.js", "sourceRoot": "", "sources": ["../../../src/Resource/middleware/express.module.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2BAA4B;AAC5B,+BAA8B;AAE9B,wDAAoD;AACpD,8DAKwC;AAEjC,MAAM,cAAc,GAAG,CAAC,OAAgB,EAAmB,EAAE,CAAC,CAAC;IACpE,IAAI,EAAE,GAAgB,EAAE,CAAC,OAAO,CAAC,IAAS;IAC1C,OAAO,EAAE,GAAgB,EAAE,CAAC,OAAO,CAAC,OAAY;IAChD,MAAM,EAAE,GAAgB,EAAE,CAAC,OAAO,CAAC,MAAW;IAC9C,KAAK,EAAE,GAAgB,EAAE,CAAC,OAAO,CAAC,KAAU;IAC5C,GAAG,EAAE,GAAgB,EAAE,CAAC,OAAO,CAAC,GAAQ;CACzC,CAAC,CAAA;AANW,QAAA,cAAc,kBAMzB;AAEK,MAAM,eAAe,GAAG,CAAC,QAAkB,EAAoB,EAAE,CAAC,CAAC;IACxE,IAAI,EAAE,CAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAgC,EAAE,EAAE;QACjE,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACtD,IAAI;YACJ,OAAO;YACP,IAAI,EAAE,IAAI,IAAI,IAAI;SACnB,CAAC,CAAA;IACJ,CAAC;CACF,CAAC,CAAA;AARW,QAAA,eAAe,mBAQ1B;AAEK,MAAM,gBAAgB,GAAG,CAAC,gBAAwB,EAAQ,EAAE;IACjE,IAAA,YAAO,EAAC,gBAAgB,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAClE,IAAI,KAAK,EAAE,CAAC;YACV,aAAK,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACzC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrB,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvB,IAAA,wBAAgB,EAAC,IAAA,cAAO,EAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBACxD,CAAC;qBAAM,IACL,IAAI,CAAC,MAAM,EAAE;oBACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAC9C,CAAC;oBACD,mBAAO,IAAA,cAAO,EAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,wCAAE,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBAC3D,aAAK,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;oBACzC,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAnBY,QAAA,gBAAgB,oBAmB5B;AAEM,MAAM,mBAAmB,GAC9B,CAAC,mBAA2B,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAC3D,mBAAO,IAAA,cAAO,EAAC,mBAAmB,EAAE,eAAe,CAAC,wCACjD,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACf,MAAM,CAAC,aAAa,CAAC;IACnB,OAAO,EAAE,IAAA,sBAAc,EAAC,OAAO,CAAC;IAChC,QAAQ,EAAE,IAAA,uBAAe,EAAC,QAAQ,CAAC;IACnC,IAAI;CACL,CAAC,CACH;KACA,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACf,aAAK,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;IAEvC,QAAQ,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;QACpC,OAAO,EAAE,oCAAoC;KAC9C,CAAC,CAAA;IAEF,IAAI,CAAC,KAAK,CAAC,CAAA;AACb,CAAC,CAAC,CAAA;AAnBK,QAAA,mBAAmB,uBAmBxB"}