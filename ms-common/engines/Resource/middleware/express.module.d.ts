import type { Request, Response } from 'express';
import { type ResourceRequest, type ResourceResponse } from '../../Resource';
export declare const prepareRequest: (request: Request) => ResourceRequest;
export declare const prepareResponse: (response: Response) => ResourceResponse;
export declare const triggerResources: (resourcePathName: string) => void;
export declare const registerCredentials: (credentialsPathName: string) => (request: any, response: any, next: any) => Promise<any>;
