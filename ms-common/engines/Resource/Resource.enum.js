"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceMessageCode = exports.ResourceCrypto = exports.ResourceExpiresIn = void 0;
var ResourceExpiresIn;
(function (ResourceExpiresIn) {
    ResourceExpiresIn["TOKEN"] = "10m";
    ResourceExpiresIn["REFRESH_TOKEN"] = "7d";
})(ResourceExpiresIn || (exports.ResourceExpiresIn = ResourceExpiresIn = {}));
var ResourceCrypto;
(function (ResourceCrypto) {
    ResourceCrypto["ALGORITHM"] = "RS256";
})(ResourceCrypto || (exports.ResourceCrypto = ResourceCrypto = {}));
// https://developer.mozilla.org/en-US/docs/Web/HTTP/Status
var ResourceMessageCode;
(function (ResourceMessageCode) {
    ResourceMessageCode["C_200_0200"] = "C_200_0200";
    ResourceMessageCode["C_200_0001"] = "C_200_0001";
    ResourceMessageCode["C_201_0201"] = "C_201_0201";
    ResourceMessageCode["C_201_0001"] = "C_201_0001";
    ResourceMessageCode["C_204_0204"] = "C_204_0204";
    ResourceMessageCode["C_204_0001"] = "C_204_0001";
    ResourceMessageCode["C_400_0400"] = "C_400_0400";
    ResourceMessageCode["C_400_0001"] = "C_400_0001";
    ResourceMessageCode["C_401_0401"] = "C_401_0401";
    ResourceMessageCode["C_401_0001"] = "C_401_0001";
    ResourceMessageCode["C_404_0404"] = "C_404_0404";
    ResourceMessageCode["C_409_0409"] = "C_409_0409";
    ResourceMessageCode["C_500_0500"] = "C_500_0500";
})(ResourceMessageCode || (exports.ResourceMessageCode = ResourceMessageCode = {}));
//# sourceMappingURL=Resource.enum.js.map