export declare enum ResourceExpiresIn {
    TOKEN = "10m",
    REFRESH_TOKEN = "7d"
}
export declare enum ResourceCrypto {
    ALGORITHM = "RS256"
}
export declare enum ResourceMessageCode {
    C_200_0200 = "C_200_0200",
    C_200_0001 = "C_200_0001",
    C_201_0201 = "C_201_0201",
    C_201_0001 = "C_201_0001",
    C_204_0204 = "C_204_0204",
    C_204_0001 = "C_204_0001",
    C_400_0400 = "C_400_0400",
    C_400_0001 = "C_400_0001",
    C_401_0401 = "C_401_0401",
    C_401_0001 = "C_401_0001",
    C_404_0404 = "C_404_0404",
    C_409_0409 = "C_409_0409",
    C_500_0500 = "C_500_0500"
}
