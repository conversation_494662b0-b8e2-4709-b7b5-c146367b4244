import { ResourceMessageCode, type ResourceResponse } from '../../Resource';
export declare class ResponseHelper {
    static handleError(error: unknown, response: ResourceResponse, entityName: string | undefined, translate: (key: string, options?: Record<string, unknown>) => string): void;
    static sendSuccessResponse<T>(response: ResourceResponse, data: T, code: ResourceMessageCode | undefined, translate: (key: string, options?: Record<string, unknown>) => string, options?: Record<string, unknown>): void;
}
