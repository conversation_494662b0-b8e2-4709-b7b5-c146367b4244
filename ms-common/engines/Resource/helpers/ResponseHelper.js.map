{"version": 3, "file": "ResponseHelper.js", "sourceRoot": "", "sources": ["../../../src/Resource/helpers/ResponseHelper.ts"], "names": [], "mappings": ";;;;;;AAAA,6BAA8B;AAE9B,wDAAoD;AACpD,8DAGwC;AACxC,qHAA4F;AAC5F,qHAA4F;AAE5F,MAAa,cAAc;IAClB,MAAM,CAAC,WAAW,CACvB,KAAc,EACd,QAA0B,EAC1B,UAAU,GAAG,QAAQ,EACrB,SAAqE;QAErE,aAAK,CAAC,KAAK,CAAC;YACV,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/D,KAAK;SACN,CAAC,CAAA;QAEF,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;YAC9B,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACjD,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBACxB,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC,CAAA;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;gBACpC,OAAO,EACL,SAAS,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;oBACpD,mBAAmB;gBACrB,IAAI,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE;aAClC,CAAC,CAAA;QACJ,CAAC;aAAM,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;gBACpC,OAAO,EAAE,SAAS,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;aAC3D,CAAC,CAAA;QACJ,CAAC;aAAM,IAAI,KAAK,YAAY,2BAAiB,EAAE,CAAC;YAC9C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;gBACpC,OAAO,EAAE,SAAS,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;aAC5D,CAAC,CAAA;QACJ,CAAC;aAAM,IAAI,KAAK,YAAY,2BAAiB,EAAE,CAAC;YAC9C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;gBACpC,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,8BAAmB,CAAC,UAAU;gBACpC,OAAO,EAAE,SAAS,CAAC,aAAa,8BAAmB,CAAC,UAAU,EAAE,CAAC;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,mBAAmB,CAC/B,QAA0B,EAC1B,IAAO,EACP,IAAI,GAAG,8BAAmB,CAAC,UAAU,EACrC,SAAqE,EACrE,OAAiC;QAEjC,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI;YACJ,OAAO,EAAE,SAAS,CAAC,aAAa,IAAI,EAAE,EAAE,OAAO,CAAC;YAChD,IAAI;SACL,CAAC,CAAA;IACJ,CAAC;CACF;AA7DD,wCA6DC"}