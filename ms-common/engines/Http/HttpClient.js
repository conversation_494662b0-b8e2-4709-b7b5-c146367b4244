"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClient = void 0;
const axios_1 = __importDefault(require("axios"));
const Debug_1 = require("../Debug");
const RequestContext_1 = require("../Http/RequestContext");
class HttpClient {
    constructor(config) {
        this.instance = axios_1.default.create(Object.assign({ timeout: 10000, headers: {
                'Content-Type': 'application/json',
            } }, config));
        this.instance.interceptors.request.use((config) => {
            var _a;
            const token = (0, RequestContext_1.getAuthorizationToken)();
            if (token) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            Debug_1.Debug.info({
                message: `HTTP Request: ${(_a = config.method) === null || _a === void 0 ? void 0 : _a.toUpperCase()} ${config.url}`,
            });
            return config;
        }, (error) => {
            Debug_1.Debug.error({
                message: 'HTTP Request Error',
                error,
            });
            return Promise.reject(error);
        });
        this.instance.interceptors.response.use((response) => {
            var _a;
            Debug_1.Debug.info({
                message: `HTTP Response: ${response.status} ${(_a = response.config.method) === null || _a === void 0 ? void 0 : _a.toUpperCase()} ${response.config.url}`,
            });
            return response;
        }, (error) => {
            Debug_1.Debug.error({
                message: 'HTTP Response Error',
                error: error.response
                    ? {
                        status: error.response.status,
                        data: error.response.data,
                        url: error.config.url,
                        method: error.config.method,
                    }
                    : error,
            });
            // @TODO: When the error is 401, we should refresh the token
            return Promise.reject(error);
        });
    }
    get(url, config) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.instance.get(url, config);
        });
    }
    post(url, data, config) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.instance.post(url, data, config);
        });
    }
    put(url, data, config) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.instance.put(url, data, config);
        });
    }
    delete(url, config) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.instance.delete(url, config);
        });
    }
    patch(url, data, config) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.instance.patch(url, data, config);
        });
    }
}
exports.HttpClient = HttpClient;
//# sourceMappingURL=HttpClient.js.map