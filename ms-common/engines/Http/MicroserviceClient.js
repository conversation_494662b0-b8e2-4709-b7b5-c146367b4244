"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MicroserviceClient = void 0;
const Http_1 = require("../Http");
class MicroserviceClient extends Http_1.HttpClient {
    constructor(config) {
        const { serviceName } = config, httpConfig = __rest(config, ["serviceName"]);
        super(httpConfig);
        this.serviceName = serviceName;
    }
    getServiceName() {
        return this.serviceName;
    }
}
exports.MicroserviceClient = MicroserviceClient;
//# sourceMappingURL=MicroserviceClient.js.map