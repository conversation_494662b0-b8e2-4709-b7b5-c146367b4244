"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearAuthorizationToken = exports.setAuthorizationToken = exports.getAuthorizationToken = exports.getRequestContext = exports.requestContextMiddleware = void 0;
const async_hooks_1 = require("async_hooks");
const Debug_1 = require("../Debug");
const asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();
let manualToken = undefined;
const requestContextMiddleware = (req, res, next) => {
    const requestContext = {
        headers: Object.assign({}, req.headers),
    };
    asyncLocalStorage.run(requestContext, () => {
        next();
    });
};
exports.requestContextMiddleware = requestContextMiddleware;
const getRequestContext = () => {
    return asyncLocalStorage.getStore();
};
exports.getRequestContext = getRequestContext;
const getAuthorizationToken = () => {
    if (manualToken) {
        return manualToken;
    }
    const context = (0, exports.getRequestContext)();
    if (!context)
        return undefined;
    const authHeader = context.headers.authorization;
    if (!authHeader)
        return undefined;
    const match = authHeader.match(/^Bearer\s+(.*)$/);
    return match ? match[1] : undefined;
};
exports.getAuthorizationToken = getAuthorizationToken;
const setAuthorizationToken = (token) => {
    Debug_1.Debug.info({ message: 'Manually setting authorization token' });
    manualToken = token;
};
exports.setAuthorizationToken = setAuthorizationToken;
const clearAuthorizationToken = () => {
    Debug_1.Debug.info({ message: 'Clearing manually set authorization token' });
    manualToken = undefined;
};
exports.clearAuthorizationToken = clearAuthorizationToken;
//# sourceMappingURL=RequestContext.js.map