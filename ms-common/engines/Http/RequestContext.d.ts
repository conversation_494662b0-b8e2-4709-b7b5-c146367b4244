import type { RequestContext } from '../Http';
export declare const requestContextMiddleware: (req: any, res: any, next: any) => void;
export declare const getRequestContext: () => RequestContext | undefined;
export declare const getAuthorizationToken: () => string | undefined;
export declare const setAuthorizationToken: (token: string) => void;
export declare const clearAuthorizationToken: () => void;
