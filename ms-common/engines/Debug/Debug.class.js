"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Debug = void 0;
class Debug {
    static info(payload) {
        // eslint-disable-next-line no-console
        console.info('%c %s ', 'color:black;background:#cdddea;', 'info', payload);
    }
    static warn(payload) {
        // eslint-disable-next-line no-console
        console.warn('%c %s ', 'color:black;background:#ffd700;', 'warn', payload);
    }
    static error(payload) {
        // eslint-disable-next-line no-console
        console.error('%c %s ', 'color:black;background:#d99cb5;', 'error', payload);
    }
}
exports.Debug = Debug;
//# sourceMappingURL=Debug.class.js.map