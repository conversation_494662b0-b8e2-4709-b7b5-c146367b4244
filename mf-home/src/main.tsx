import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "@app/domain/locales/i18n/i18next";

import "@app/styles/globals.css";

import App from "@app/App";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </StrictMode>
);
