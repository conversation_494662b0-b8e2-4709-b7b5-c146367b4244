import { useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { LoginParams } from "./Login";
import { postLogin } from "./Login.api";

export const useLoginMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: LoginParams) => postLogin(params),
  });

  const postLoginAsync = useCallback(
    async (props: LoginParams) => {
      const response = await mutateAsync(props);
      return response.data;
    },
    [mutateAsync]
  );

  return {
    postLoginAsync,
    loading: isPending,
  };
};
