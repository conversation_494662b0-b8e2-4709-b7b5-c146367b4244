import { resolve } from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import config from './app.config.json'

const rootPath = resolve(__dirname, '.')
const sourcePath = resolve(rootPath, './src')

// https://vite.dev/config/
export default defineConfig({
  base: '/home',
  root: rootPath,
  plugins: [react(), tailwindcss()],
  build: {
    sourcemap: true,
    emptyOutDir: true,
    outDir: resolve(rootPath, config.build.outDir),
  },
  resolve: {
    alias: { '@app': sourcePath },
  },
})
