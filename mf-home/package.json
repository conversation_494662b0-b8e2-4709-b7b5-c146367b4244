{"name": "mf-home", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@tanstack/react-query": "5.77.0", "@thrift/design-system": "https://github.com/thrift-technology/design-system.git#main", "axios": "1.9.0", "i18next": "25.2.1", "i18next-browser-languagedetector": "8.1.0", "lucide-react": "0.511.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.4", "react-router-dom": "^7.6.1", "react-i18next": "15.5.2", "zod": "3.25.28"}, "devDependencies": {"@eslint/js": "9.27.0", "@tailwindcss/vite": "4.1.7", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "@vitejs/plugin-react": "4.5.0", "eslint": "9.25.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react-hooks": "6.0.0", "eslint-plugin-react-refresh": "0.4.20", "globals": "16.2.0", "prettier": "3.5.3", "tailwindcss": "4.1.7", "typescript": "5.8.3", "typescript-eslint": "8.32.1", "vite": "6.3.5"}}