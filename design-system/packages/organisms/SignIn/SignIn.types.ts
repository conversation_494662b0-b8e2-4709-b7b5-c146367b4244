import type { HTMLAttributes } from 'react'
import type { InputTextProps } from '@thrift/design-system/packages/molecules/InputText/'
import type { ButtonProps } from '@thrift/design-system/packages/molecules/Button'

export type SignInProps = {
  title: string
  description: string
  userNameProps: InputTextProps
  passwordProps: InputTextProps
  buttonProps: ButtonProps
} & HTMLAttributes<HTMLDivElement>
