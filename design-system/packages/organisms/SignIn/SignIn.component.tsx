import { LoginRightSide } from '@thrift/design-system/packages/templates/Login'
import { Title } from '@thrift/design-system/packages/molecules/Title'
import { Paragraph } from '@thrift/design-system/packages/molecules/Paragraph'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import type { SignInProps } from '@thrift/design-system/packages/organisms/SignIn'
import { But<PERSON> } from '@thrift/design-system/packages/molecules/Button'

export const SignIn = ({
  title,
  description,
  userNameProps,
  passwordProps,
  buttonProps,
}: SignInProps) => {
  return (
    <LoginRightSide>
      <Title>{title}</Title>
      <Paragraph>{description}</Paragraph>
      <InputText {...userNameProps} />
      <InputText {...passwordProps} />
      <div className="flex align-middle self-end">
        <Button {...buttonProps} />
      </div>
    </LoginRightSide>
  )
}
