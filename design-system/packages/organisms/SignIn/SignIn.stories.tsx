import { faker } from '@faker-js/faker'
import type { Meta, StoryObj } from '@storybook/react'
import {
  SignIn,
  type SignInProps,
} from '@thrift/design-system/packages/organisms/SignIn'

const meta = {
  title: 'Organisms/SignIn',
  component: SignIn,
  argTypes: {
    title: { control: 'text' },
    description: { control: 'text' },
  },
} satisfies Meta<SignInProps>

export default meta

type Story = StoryObj<SignInProps>

export const SignInDefault: Story = {
  name: 'SignIn',
  args: {
    title: faker.company.name(),
    description: faker.lorem.sentences(3),
  },
}
