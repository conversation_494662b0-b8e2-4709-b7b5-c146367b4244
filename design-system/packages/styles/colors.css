@theme {
  /* Black and White */
  --color-black: oklch(0 0 0 / 1);
  --color-white: oklch(1 0 0 / 1);

  /* Gray Scale */
  --color-gray-100: oklch(0.98 0 0 / 1);
  --color-gray-200: oklch(0.93 0 0 / 1);
  --color-gray-300: oklch(0.88 0 0 / 1);
  --color-gray-400: oklch(0.83 0 0 / 1);
  --color-gray-500: oklch(0.78 0 0 / 1);
  --color-gray-600: oklch(0.73 0 0 / 1);
  --color-gray-700: oklch(0.68 0 0 / 1);
  --color-gray-800: oklch(0.63 0 0 / 1);
  --color-gray-900: oklch(0.57 0 0 / 1);

  /* Blue Scale */
  --color-blue-light: oklch(93.2% 0.032 255.585);
  --color-blue: oklch(62.3% 0.214 259.815);
  --color-blue-dark: oklch(37.9% 0.146 265.522);

  /* Green Scale */
  --color-green-light: oklch(96.2% 0.044 156.743);
  --color-green: oklch(72.3% 0.219 149.579);
  --color-green-dark: oklch(39.3% 0.095 152.535);

  /* Yellow Scale */
  --color-yellow-light: oklch(97.3% 0.071 103.193);
  --color-yellow: oklch(79.5% 0.184 86.047);
  --color-yellow-dark: oklch(42.1% 0.095 57.708);

  /* Red Scale */
  --color-red-light: oklch(93.6% 0.032 17.717);
  --color-red: oklch(63.7% 0.237 25.331);
  --color-red-dark: oklch(39.6% 0.141 25.723);

  /* Brand Colors */
  --color-primary: oklch(0.15 0.06 260.42 / 1);
  --color-secondary: oklch(0.43 0.08 181.24 / 1);
  --color-tertiary: oklch(0.69 0.15 162.77 / 1);

  /* Backgroud */
  --color-backgroud-light: var(--color-gray-100);
}
