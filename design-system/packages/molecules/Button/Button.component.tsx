import type { ButtonProps } from '@thrift/design-system/packages/molecules/Button'

export const Button = ({ label, ...rest }: ButtonProps) => {
  return (
    <div className="relative w-full z-0">
      <button
        type="button"
        className="
        text-white 
        bg-blue-700 
        font-medium 
        rounded-lg 
        text-sm 
        px-5 
        py-2.5 
        me-2 mb-2 
        dark:bg-blue-600 
        focus:outline-none 
        dark:focus:ring-blue-800
        p-8"
        {...rest}
      >
        {label}
      </button>
    </div>
  )
}
