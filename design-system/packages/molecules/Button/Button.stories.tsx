import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import {
  Button,
  type ButtonProps,
} from '@thrift/design-system/packages/molecules/Button'

const meta = {
  title: 'Molecules/Button',
  component: Button,
  argTypes: {
    label: { control: 'text' },
  },
} satisfies Meta<ButtonProps>

export default meta

type Story = StoryObj<ButtonProps>

export const ButtonDefault: Story = {
  name: 'Button',
  args: {
    label: 'Button',
  },
}
