import type { InputTextProps } from '@thrift/design-system/packages/molecules/InputText'

export const InputText = ({ label, ...rest }: InputTextProps) => {
  return (
    <div className="relative w-full z-0">
      <input
        type="text"
        placeholder=""
        className="
          block
          py-tiny
          px-0
          w-full
          text-small
          border-b
          peer
          text-black
          border-gray-300
          focus:border-b-secondary
          focus:outline-none
        "
        {...rest}
      />
      <label
        className="
          absolute text-tiny
          text-gray-500
          dark:text-gray-400
          duration-300 transform
          -translate-y-6 scale-75
          top-3 -z-10 origin-[0]
          peer-focus:start-0
          peer-focus:text-tertiary
          peer-focus:dark:text-secondary
          peer-placeholder-shown:scale-100
          peer-placeholder-shown:translate-y-0
          peer-focus:scale-75
          peer-focus:-translate-y-6
          rtl:peer-focus:translate-x-1/4
          rtl:peer-focus:left-aut
        "
      >
        {label}
      </label>
    </div>
  )
}
