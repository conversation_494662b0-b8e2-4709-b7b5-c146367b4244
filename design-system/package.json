{"name": "@thrift/design-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"build": "yarn healthcheck && tsc -b && vite build", "lint": "eslint .", "lint:fix": "yarn lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "typecheck": "tsc --noEmit", "healthcheck": "yarn lint && yarn format && yarn typecheck", "preview": "vite preview", "storybook": "yarn healthcheck && storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "prepare": "husky"}, "peerDependencies": {"classnames": "2.5.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "4.1.5"}, "dependencies": {"classnames": "2.5.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "4.1.5"}, "devDependencies": {"@chromatic-com/storybook": "3", "@eslint/js": "9.25.1", "@faker-js/faker": "^9.7.0", "@storybook/addon-essentials": "8.6.12", "@storybook/addon-onboarding": "8.6.12", "@storybook/blocks": "8.6.12", "@storybook/react": "8.6.12", "@storybook/react-vite": "8.6.12", "@tailwindcss/vite": "4.1.5", "@types/node": "22.15.3", "@types/react": "19.1.2", "@types/react-dom": "19.1.3", "@vitejs/plugin-react": "4.4.1", "@vitest/browser": "3.1.2", "@vitest/coverage-v8": "3.1.2", "eslint": "9.25.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react-hooks": "6.0.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-storybook": "0.12.0", "globals": "16.0.0", "husky": "^9.1.7", "prettier": "3.5.3", "storybook": "8.6.12", "typescript": "5.8.3", "typescript-eslint": "8.31.1", "vite": "6.3.4", "vitest": "3.1.2"}, "engines": {"node": ">=20 <24", "npm": ">=10 <12", "yarn": ">=1.22 <2"}}