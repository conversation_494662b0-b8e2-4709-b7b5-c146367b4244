model Purchase {
  id                    String   @id @default(uuid())
  supplierId            String   // FK to Person.id from ms-person
  storeId               String   // FK to Store.id or TenantStore.id
  amount                Decimal  @default(0)
  currency              String   @default("BRL")
  originatingProposalId String?  // ID of the EvaluationProposal from ms-evaluation for traceability
  notes                 String?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  deleted               Boolean  @default(false)

  @@map("purchases")
}
