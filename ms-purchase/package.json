{"name": "@thrift/purchase", "version": "1.0.0", "private": true, "description": "purchase application service", "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-purchase.git"}, "prisma": {"schema": "./prisma/schema"}, "engines": {"node": ">=20 <25", "npm": ">=10 <12", "yarn": ">=1.22 <2"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": "yarn healthcheck && tsx watch src/main.ts", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "start": "node dist/src/main.js"}, "dependencies": {"@prisma/client": "6.8.2", "@thrift/common": "https://github.com/thrift-technology/ms-common.git#main", "i18next": "25.2.0", "i18next-browser-languagedetector": "8.1.0"}, "devDependencies": {"@types/node": "22.15.20", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-unused-imports": "4.1.4", "prettier": "3.5.3", "prisma": "6.8.2", "tsc-alias": "1.8.16", "tsx": "4.19.4", "typescript": "5.8.3", "typescript-eslint": "8.32.1"}}