## Important: Base Repository Purpose

This is a base repository that serves as a template for other microservices. We use this repository solely as a source for copying templates to other microservice repositories (like wallet, person, account, stock, etc.).

## Using Make Commands

To properly use this repository with other microservices:

1. Make sure you have cloned all required microservice repositories (wallet, person, account, stock) alongside this base repository
2. Use the `make` command to run operations across repositories
3. The directory structure should be:
   ```
   /parent-directory
     /ms-base
     /ms-wallet
     /ms-person
     /ms-account
     /ms-stock
   ```
4. Run make commands from the parent directory to ensure proper template propagation
