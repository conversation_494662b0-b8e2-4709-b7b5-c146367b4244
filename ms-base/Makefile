# Variables
SHELL := /bin/bash

# List of services to update
SERVICES := account person stock wallet purchase evaluation

# Default target
.PHONY: help
help:
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: new-service
new-service: ## Create a new service (usage: make new-service SERVICE=name)
	@if [ -z "$(SERVICE)" ]; then \
		echo "Error: SERVICE parameter is required"; \
		echo "Usage: make new-service SERVICE=name"; \
		exit 1; \
	fi
    @echo "Creating new service: $(SERVICE)"
	@mkdir -p ms-$(SERVICE)
	@cp -r .credentials ms-$(SERVICE)/
	@cp .dockerignore ms-$(SERVICE)/
	@cp .editorconfig ms-$(SERVICE)/
	@cp .gitignore ms-$(SERVICE)/
	@cp .prettierignore ms-$(SERVICE)/
	@cp .prettierrc.js ms-$(SERVICE)/
	@cp -r .scripts ms-$(SERVICE)/
	@cp -r .vscode ms-$(SERVICE)/
	@cp app.config.sample.json ms-$(SERVICE)/
	@cp docker-compose.yml ms-$(SERVICE)/
	@cp eslint.config.js ms-$(SERVICE)/
	@cp tsconfig.json ms-$(SERVICE)/
	@sed "s/SERVICE_NAME/$(SERVICE)/g" package.json > ms-$(SERVICE)/package.json
	@mkdir -p ms-$(SERVICE)/prisma/schema
	@echo "New service '$(SERVICE)' created at 'ms-$(SERVICE)'"

.PHONY: update-services
update-services: ## Update all existing services
	@echo "Updating all services..."
	@for service in $(SERVICES); do \
		$(MAKE) update-service SERVICE=$$service; \
	done
	@echo "All services updated successfully"

.PHONY: update-service
update-service: ## Update a specific service (usage: make update-service SERVICE=name)
	@if [ -z "$(SERVICE)" ]; then \
		echo "Error: SERVICE parameter is required"; \
		echo "Usage: make update-service SERVICE=name"; \
		exit 1; \
	fi
	@TARGET_DIR="../ms-$(SERVICE)"; \
	if [ ! -d "$$TARGET_DIR" ]; then \
		echo "Directory $$TARGET_DIR does not exist. Skipping."; \
		exit 1; \
	fi; \
	echo "Updating $$TARGET_DIR..."; \
	cp -r .credentials "$$TARGET_DIR/"; \
	cp .dockerignore "$$TARGET_DIR/"; \
	cp .editorconfig "$$TARGET_DIR/"; \
	cp .prettierignore "$$TARGET_DIR/"; \
	cp .prettierrc.js "$$TARGET_DIR/"; \
	cp .gitignore "$$TARGET_DIR/"; \
	cp eslint.config.js "$$TARGET_DIR/"; \
	mkdir -p "$$TARGET_DIR/.scripts"; \
	cp .scripts/build.sh "$$TARGET_DIR/.scripts/"; \
	cp .scripts/format.sh "$$TARGET_DIR/.scripts/"; \
	cp .scripts/install.sh "$$TARGET_DIR/.scripts/"; \
	cp .scripts/lint.sh "$$TARGET_DIR/.scripts/"; \
	cp .scripts/provisioning.sh "$$TARGET_DIR/.scripts/"; \
	mkdir -p "$$TARGET_DIR/.scripts/provisioning"; \
	cp .scripts/provisioning/env.sh "$$TARGET_DIR/.scripts/provisioning/"; \
	cp .scripts/provisioning/migrations.sh "$$TARGET_DIR/.scripts/provisioning/"; \
	cp .scripts/provisioning/seed.sh "$$TARGET_DIR/.scripts/provisioning/"; \
	cp .scripts/provisioning/credentials.sh "$$TARGET_DIR/.scripts/provisioning/"; \
	mkdir -p "$$TARGET_DIR/.vscode"; \
	cp -r .vscode/* "$$TARGET_DIR/.vscode/"; \
	cp tsconfig.json "$$TARGET_DIR/"; \
	TARGET_FILE="$$TARGET_DIR/package.json"; \
	if [ -f "$$TARGET_FILE" ]; then \
		echo "Updating package.json..."; \
		TMP_FILE=$$(mktemp); \
		node -e " \
			const fs = require('fs'); \
			const template = JSON.parse(fs.readFileSync('package.json', 'utf8')); \
			const target = JSON.parse(fs.readFileSync('$$TARGET_FILE', 'utf8')); \
			\
			/* Update scripts - replace completely */ \
			target.scripts = template.scripts; \
			\
			/* Update prisma - replace completely */ \
			target.prisma = template.prisma; \
			\
			/* Update engines - replace completely */ \
			target.engines = template.engines; \
			\
			/* Update devDependencies - merge without removing existing entries */ \
			target.devDependencies = { ...target.devDependencies, ...template.devDependencies }; \
			\
			/* Update dependencies - merge without removing existing entries */ \
			target.dependencies = { ...target.dependencies, ...template.dependencies }; \
			\
			/* Sort dependencies and devDependencies alphabetically */ \
			target.devDependencies = Object.keys(target.devDependencies).sort().reduce((obj, key) => { \
				obj[key] = target.devDependencies[key]; \
				return obj; \
			}, {}); \
			target.dependencies = Object.keys(target.dependencies).sort().reduce((obj, key) => { \
				obj[key] = target.dependencies[key]; \
				return obj; \
			}, {}); \
			/* Write the updated package.json to a temporary file */ \
			fs.writeFileSync('$$TMP_FILE', JSON.stringify(target, null, 2) + '\\n'); \
			\
		"; \
		mv "$$TMP_FILE" "$$TARGET_FILE"; \
	else \
        echo "Creating package.json..."; \
        sed "s/SERVICE_NAME/$(SERVICE)/g" package.json > "$$TARGET_FILE"; \
    fi; \
	echo "Updated $$TARGET_DIR successfully"

.PHONY: build-all
build-all: ## Build all microservices
	@echo "Building all services..."
	@for service in $(SERVICES); do \
		echo "Building service: $$service"; \
		cd ../ms-$$service && docker-compose run --rm build; \
	done
	@echo "All services built successfully"
