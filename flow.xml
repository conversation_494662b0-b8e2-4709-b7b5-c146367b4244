<mxfile host="app.diagrams.net">
  <diagram name="Thrift Technology - Avaliação, Compra e Venda" id="thrift-tech-flow-001">
    <mxGraphModel dx="900" dy="600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Início -->
        <mxCell id="start" value="Início" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="60" width="100" height="40" as="geometry"/>
        </mxCell>

        <!-- Cadastro/Autenticação -->
        <mxCell id="account" value="Cadastro/Autenticação (ms-account/ms-person)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="220" y="60" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="accountEdge" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start" target="account">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Submissão de Itens para Avaliação -->
        <mxCell id="submitEval" value="Submissão de itens para avaliação (ms-evaluation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="500" y="60" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="submitEvalEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="account" target="submitEval">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Avaliação dos Itens -->
        <mxCell id="evalBatch" value="Avaliação dos itens (ms-evaluation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="780" y="60" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="evalBatchEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="submitEval" target="evalBatch">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Proposta de Compra -->
        <mxCell id="proposal" value="Proposta de compra para fornecedor (ms-evaluation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1060" y="60" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="proposalEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="evalBatch" target="proposal">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Decisão do Fornecedor -->
        <mxCell id="decision" value="Fornecedor aceita proposta?" style="rhombus;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1340" y="70" width="80" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="decisionEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="proposal" target="decision">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Fluxo: Aceita -->
        <mxCell id="accept" value="Pagamento e recebimento dos itens (ms-wallet/ms-stock)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1500" y="30" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="acceptEdge" value="Sim" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="decision" target="accept">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1420" y="50" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Fluxo: Recusa -->
        <mxCell id="reject" value="Itens devolvidos ao fornecedor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1500" y="110" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="rejectEdge" value="Não" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="decision" target="reject">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1420" y="130" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Entrada em Estoque -->
        <mxCell id="stock" value="Entrada dos itens no estoque (ms-stock)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1750" y="30" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="stockEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="accept" target="stock">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Disponibilização para Venda -->
        <mxCell id="sale" value="Disponibilização para venda (ms-stock/ms-wallet)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="2000" y="30" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="saleEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="stock" target="sale">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Venda ao Cliente Final -->
        <mxCell id="finalSale" value="Venda ao cliente final (ms-wallet/ms-account)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="2250" y="30" width="220" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="finalSaleEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="sale" target="finalSale">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Fim -->
        <mxCell id="end" value="Fim" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="2500" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="endEdge" style="edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="finalSale" target="end">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

