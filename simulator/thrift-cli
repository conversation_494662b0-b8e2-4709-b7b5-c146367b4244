#!/bin/bash
set -eux

# Define variables
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/./" && pwd)"

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --build)
      /bin/bash ${_root_path}/cli/database --up \
        && /bin/bash ${_root_path}/cli/macro-service --build \
        && /bin/bash ${_root_path}/cli/api-gateway --build \
        && /bin/bash ${_root_path}/cli/macro-frontend --build \
        && /bin/bash ${_root_path}/cli/cdn --build < /dev/null
      ;;

    --up)
      /bin/bash ${_root_path}/cli/database --up \
        && /bin/bash ${_root_path}/cli/macro-service --up\
        && /bin/bash ${_root_path}/cli/api-gateway --up\
        && /bin/bash ${_root_path}/cli/cdn --up < /dev/null
      ;;

    --down)
      /bin/bash ${_root_path}/cli/api-gateway --down < /dev/null
      /bin/bash ${_root_path}/cli/cdn --cleanup < /dev/null
      /bin/bash ${_root_path}/cli/macro-service --down < /dev/null
      /bin/bash ${_root_path}/cli/database --down < /dev/null
      ;;

    --cleanup)
      /bin/bash ${_root_path}/cli/api-gateway --cleanup < /dev/null
      /bin/bash ${_root_path}/cli/cdn --cleanup < /dev/null
      /bin/bash ${_root_path}/cli/macro-service --cleanup < /dev/null
      /bin/bash ${_root_path}/cli/macro-frontend --cleanup < /dev/null
      /bin/bash ${_root_path}/cli/database --cleanup < /dev/null
      ;;
  esac
done

exit 0
