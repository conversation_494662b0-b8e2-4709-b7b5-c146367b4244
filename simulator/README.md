# Simulator

Thrift Technologies Application Simulator

## Packages

- _**cli**_ applications to execute simulator commands;
- _**docker**_ docker compose files used to execute applications;
- _**environment**_ list of applications that will be compose.
- _**apps**_ apps cache folder.
- _**.vscode**_ workspace configuration to [vscode](https://code.visualstudio.com/docs/editor/workspaces#_singlefolder-workspace-settings).

## How to use this Application

### Github Personal Access Token

[Creating a personal access token (classic)](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic) in the [github](https://github.com/).

- **Note:** `personal-token-github`
- **Expiration:** `No expiration`
- **Select scopes:** `notifications`, `read:packages`, `repo`, and `workflow`

After that, inside in the directory repository, execute the command below:

```bash
$ export PERSONAL_TOKEN_GITHUB=<<personal-token-github>>
$ git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
```

_Replace the `<<personal-token-github>>` to your Github Personal Access Token_

### Docker

You will need [docker](https://docs.docker.com/engine/install) and [docker-compose](https://docs.docker.com/compose/install) to use the commands below. It's necessary to access the repository root path.

### How to run

1. Build all apps `./thrift-cli --build`
2. Up all apps `./thrift-cli --up`

_P.S. Always look at the LOGs_

## Commands

Build all apps.

```bash
$ ./thrift-cli --build
```

Up all apps.

```bash
$ ./thrift-cli --up
```

Down all apps.

```bash
$ ./thrift-cli --down
```

Clean up all apps data.

```bash
$ ./thrift-cli --cleanup
```

### database

Up database service.

```bash
$ ./cli/database --up
```

Down database service.

```bash
$ ./cli/database --down
```

Clean up all database service data.

```bash
$ ./cli/database --cleanup
```

How to access the database.

```bash
docker run --rm -it  mysql:8 mysql -h host.docker.internal -u root -p
```

### macro-services

Build all macro-services.

```bash
$ ./cli/macro-service --build
```

Up all macro-services.

```bash
$ ./cli/macro-service --up
```

Down all macro-services.

```bash
$ ./cli/macro-service --down
```

Clean up all macro-services service.

```bash
$ ./cli/macro-service --cleanup
```

### api-gateway

Build all api-gateway service.

```bash
$ ./cli/api-gateway --build
```

Up all api-gateway service.

```bash
$ ./cli/api-gateway --up
```

Down all api-gateway service.

```bash
$ ./cli/api-gateway --down
```

Clean up all api-gateway service data.

```bash
$ ./cli/api-gateway --cleanup
```

### macro-frontends

Build all macro-frontends files.

```bash
$ ./cli/macro-frontend --build
```

Down all macro-frontends files.

```bash
$ ./cli/macro-frontend --down
```

Clean up all macro-frontends files data.

```bash
$ ./cli/macro-frontend --cleanup
```

### CDN ([Content delivery network](https://en.wikipedia.org/wiki/Content_delivery_network))

Build all CDN service.

```bash
$ ./cli/cdn --build
```

Up all CDN service.

```bash
$ ./cli/cdn --up
```

Down all CDN service.

```bash
$ ./cli/cdn --down
```

Clean up all CDN service data.

```bash
$ ./cli/cdn --cleanup
```
