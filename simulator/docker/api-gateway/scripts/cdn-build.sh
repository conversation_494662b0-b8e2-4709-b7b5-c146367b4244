#!/bin/bash
set -eux

# Service name
_server_config_dir="${STORAGE_DIR}/includes"
_server_config_filename="${_server_config_dir}/${APP_NAME}.cdn.conf"
_app_config_filename="${WORKING_DIR}/dist/app.config.json"
_app_deployment_type=$(node -e "console.log(require('${_app_config_filename}').deployment.type)")
_app_deployment_resource=$(node -e "console.log(require('${_app_config_filename}').deployment.resource)")
_app_server_port=$(node -e "console.log(require('${_app_config_filename}').server.port)")
_cdn_resource_url="http://host.docker.internal:${_app_server_port}/"

# Exit if not an API service
[ "${_app_deployment_type}" != "cdn" ] && exit 0

# Remove cache
rm -rf "${_server_config_filename}"
mkdir -p "${_server_config_dir}"

# Create Nginx configuration
sed \
  -e "s#:{LOCATION_PATH}#$_app_deployment_resource#g" \
  -e "s#:{PROXY_PASS}#$_cdn_resource_url#g" \
  "${SCRIPTS_DIR}/templates/cdn.conf.template" \
  > "${_server_config_filename}"

exit 0