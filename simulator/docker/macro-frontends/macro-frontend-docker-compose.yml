name: thrift
services:
  macro-frontend-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/github.env
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/macro-frontend-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/storage

  cleanup:
    container_name: api-gateway-cleanup
    image: node:22-alpine
    working_dir: /usr/src/app
    volumes:
      - ../../.cache/apps:/usr/src/app
    command: find /usr/src/app -maxdepth 1 -type d -name "mf-*" -exec rm -rf {} +
