name: thrift
services:
  macro-service-build:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/github.env
      - ../variables/paths.env
    command: /bin/bash /usr/src/scripts/macro-service-build.sh
    volumes:
      - ./scripts:/usr/src/scripts
      - ../../.cache/apps:/usr/src/storage

  macro-service-runtime:
    image: node:22-alpine
    working_dir: /usr/src/app
    env_file:
      - ../variables/database.env
    command: /usr/local/bin/node /usr/src/app/dist/src/main.js

  cleanup:
    container_name: api-gateway-cleanup
    image: node:22-alpine
    working_dir: /usr/src/app
    volumes:
      - ../../.cache/apps:/usr/src/app
    command: find /usr/src/app -maxdepth 1 -type d -name "ms-*" -exec rm -rf {} +
