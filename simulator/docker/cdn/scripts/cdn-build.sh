#!/bin/bash
set -eux

# Service name
_server_html_dir="${STORAGE_DIR}/html"
_server_html_app_dir="${_server_html_dir}/${APP_NAME}"
_server_config_dir="${STORAGE_DIR}/includes"
_server_config_filename="${_server_config_dir}/${APP_NAME}.proxy.conf"
_app_config_filename="${WORKING_DIR}/app.config.json"
_app_deployment_type=$(node -e "console.log(require('${_app_config_filename}').deployment.type)")
_app_deployment_resource=$(node -e "console.log(require('${_app_config_filename}').deployment.resource)")

# Exit if not an API service
[ "${_app_deployment_type}" != "cdn" ] && exit 0

# Remove cache
rm -rf "${_server_config_filename}" "${_server_html_app_dir}"
mkdir -p "${_server_config_dir}"
mkdir -p "${_server_html_dir}"

# Copy static files
cp -r "${WORKING_DIR}/dist" "${_server_html_app_dir}"

# Create Nginx configuration
sed \
  -e "s#:{LOCATION_PATH}#$_app_deployment_resource#g" \
  -e "s#:{APP_NAME}#$APP_NAME#g" \
  "${SCRIPTS_DIR}/templates/cdn.conf.template" \
  > "${_server_config_filename}"

exit 0