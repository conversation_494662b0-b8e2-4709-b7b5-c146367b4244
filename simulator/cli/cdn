#!/bin/bash
set -eux

# Define variables
_working_dir='/usr/src/app'
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/cdn/cdn-docker-compose.yml"

function _build() {
  docker compose --file ${_docker_compose_filename} pull cdn-build < /dev/null

  for service in "${_root_path}/.cache/apps"/mf-*/; do
    [ ! -d "${service}" ] && continue

    local _macroService=$(basename "${service}")

    docker compose \
      --file ${_docker_compose_filename} \
      run --rm --detach --no-TTY \
      --volume "${service}:${_working_dir}" \
      --env APP_NAME=${_macroService} \
      cdn-build < /dev/null
  done
}

function _up() {
  docker compose --file ${_docker_compose_filename} pull cdn-runtime < /dev/null

  docker compose \
    --file ${_docker_compose_filename} \
    up --detach --build cdn-runtime < /dev/null
}

function _down() {
  docker compose \
    --file ${_docker_compose_filename} \
    down cdn-runtime --volumes < /dev/null
}

function _cleanup() {
  docker compose \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --build)
      _build
      ;;

    --up)
      _up
      ;;

    --down)
      _down
      ;;

    --cleanup)
      _down
      _cleanup
      ;;
  esac
done

exit 0
