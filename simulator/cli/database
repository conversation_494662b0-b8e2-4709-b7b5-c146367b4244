#!/bin/bash
set -eux

# Define variables
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/database/database-docker-compose.yml"
_docker_container_name="thrift_database"

function _up() {
  docker compose --file ${_docker_compose_filename} pull database < /dev/null

  docker compose \
    --file ${_docker_compose_filename} \
    up --detach --build database < /dev/null
}

function _down() {
  docker compose \
    --file ${_docker_compose_filename} \
    down database --volumes < /dev/null
}

function _cleanup() {
  docker compose \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --up)
      _up
      ;;

    --down)
    _down
      ;;

    --cleanup)
      _down
      _cleanup
      ;;
  esac
done

exit 0
