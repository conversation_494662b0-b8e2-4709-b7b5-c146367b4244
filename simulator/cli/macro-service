#!/bin/bash
set -eux

# Define variables
_working_dir='/usr/src/app'
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/macro-services/macro-service-docker-compose.yml"
_macro_services_filename="${_root_path}/environment/macro-services.list"

function _build() {
  docker compose --file ${_docker_compose_filename} pull macro-service-build < /dev/null

  while IFS= read -r _macroService; do
    [ -z $_macroService ] && continue
    docker compose \
      --file ${_docker_compose_filename} \
      run --rm --no-TTY \
      --name "${_macroService}-build" \
      --env APP_NAME=${_macroService} \
      macro-service-build < /dev/null
  done < "$_macro_services_filename"
}

function _up() {
  docker compose --file ${_docker_compose_filename} pull macro-service-runtime < /dev/null

  for _macroServiceFilename in "${_root_path}/.cache/apps"/ms-*/; do
    [ ! -d "${_macroServiceFilename}" ] && continue

    _macroService=$(basename "${_macroServiceFilename}")
    _server_port=$(grep -m 1 '"port":' "${_macroServiceFilename}/dist/app.config.json" | sed -E 's/.*"port": ([0-9]+).*/\1/')

    docker compose \
      --file ${_docker_compose_filename} \
      run --rm --detach --no-TTY \
      --volume "${_macroServiceFilename}:${_working_dir}" \
      --name "${_macroService}-runtime" \
      --publish "${_server_port}:${_server_port}" \
      macro-service-runtime < /dev/null
  done
}

function _down() {
  docker ps -a --format "{{.Names}}" | grep "^ms-" | xargs docker rm --volumes --force
}

function _cleanup() {
  docker compose \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --build)
      _build
      ;;

    --up)
      _up
      ;;

    --down)
      _down
      ;;

    --cleanup)
      _down
      _cleanup
      ;;
  esac
done

exit 0
